"""
MCP统一管理器

职责：
- 协调配置、缓存、格式化等组件
- 提供统一的对外接口
- 管理工具生命周期
"""

import logging
import json
from typing import Dict, List

from langchain_core.tools import BaseTool
from langchain_core.utils.function_calling import convert_to_openai_tool

from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager

logger = logging.getLogger(__name__)


class MCPToolManager:
    """MCP统一管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.cache_manager = MCPCacheManager()

    async def get_enabled_mcp_tools(self) -> List[BaseTool]:
        """
        获取所有MCP工具
        
        Returns:
            Dict: {server_name: [tools]}
        """
        try:
            tools_dict = await self.cache_manager.get_tools()
            all_tools = []
            for server_tools in tools_dict.values():
                # 防护检查：确保server_tools不为None
                if server_tools is None:
                    logger.warning("Found None server_tools in get_enabled_mcp_tools, skipping")
                    continue
                all_tools.extend(server_tools)
            return all_tools
        except Exception as e:
            import traceback
            logger.error(f"Failed to get tools: {e}\nTraceback: {traceback.format_exc()}")
            return []
    
    async def get_enabled_mcp_tools_description(self, tool_names: List[str] = None) -> str:
        """
        获取工具描述文档
        
        Args:
            tool_names: 指定的工具名称列表，如果为空则不过滤
        
        Returns:
            str: Markdown格式的工具描述
        """
        try:
            # 获取所有工具
            tools_dict = await self.cache_manager.get_tools()
            
            # 如果指定了tool_names，进行过滤
            if tool_names:
                filtered_tools_dict = {}
                for server_name, tools in tools_dict.items():
                    # 防护检查：确保tools不为None
                    if tools is None:
                        logger.warning(f"Found None tools for server {server_name}, skipping")
                        continue
                    filtered_tools = [tool for tool in tools if tool.name in tool_names]
                    if filtered_tools:
                        filtered_tools_dict[server_name] = filtered_tools
                tools_dict = filtered_tools_dict
                logger.info(f"Filtered tools by names: {tool_names}")
            
            if tools_dict:
                logger.info(f"Found {len(tools_dict)} servers in tools_dict")
                for server_name, tools in tools_dict.items():
                    # 防护检查：确保tools不为None
                    if tools is None:
                        logger.warning(f"Found None tools for server {server_name}")
                        continue
                    logger.info(f"Server '{server_name}': {len(tools)} tools")
                    if tools:
                        current_tool_names = [tool.name for tool in tools]
                        logger.info(f"  Tool names: {current_tool_names}")
            
            # 检查是否有有效工具
            total_tools = sum(len(tools or []) for tools in tools_dict.values()) if tools_dict else 0
            logger.info(f"Total tools count: {total_tools}")
            
            if not tools_dict or total_tools == 0:
                logger.warning(f"No valid MCP tools found. tools_dict: {bool(tools_dict)}, total_tools: {total_tools}")
                return "## Available Tools\nNo MCP tools available."
            
            # 格式化描述
            description = self._format_tools_description(tools_dict)
            
            # 记录最终返回的工具数量（直接统计实际工具数量）
            final_tool_count = sum(len(tools or []) for tools in tools_dict.values())
            logger.info(f"Final description contains {final_tool_count} tools")
            
            return description
            
        except Exception as e:
            logger.error(f"Failed to get tools description: {e}")
            import traceback
            logger.error(f"Full traceback:\n{traceback.format_exc()}")
            return "## Available Tools\nError generating tools description."

    def _format_tools_description(self, tools_dict: Dict[str, List[BaseTool]]) -> str:
        """
        将所有工具格式化为清晰且可读性强的 Markdown 说明：
        - 每个 MCP服务 分组
        - 每个工具之间增加清晰的分隔
        - 明确展示 工具名/描述/输入参数/输出信息
        """
        if not tools_dict:
            return "### MCP 可用工具\n暂无可用的 MCP 工具。"

        sections: List[str] = ["### MCP 可用工具"]
        for server_name, tools in tools_dict.items():
            # 防护检查：确保tools不为None
            if tools is None:
                logger.warning(f"Found None tools for server {server_name} in _format_tools_description, skipping")
                continue
            if not tools:
                continue
            sections.append(f"\n#### MCP服务：{server_name}")
            tool_blocks: List[str] = []
            for tool in tools:
                try:
                    block = self._format_single_tool(tool, server_name)
                    tool_blocks.append(block)
                except Exception as e:
                    logger.error(f"Failed to format tool '{tool.name}': {e}")
            if tool_blocks:
                # 用水平分割线分隔每个工具
                sections.append("\n---\n".join(tool_blocks))
        if len(sections) == 1:
            return "### MCP 可用工具\n未找到已启用的 MCP 工具。"
        return "\n\n".join(sections)

    def _format_single_tool(self, tool: BaseTool, server_name: str = "") -> str:
        """
        以美观、结构化的方式格式化单个工具：
        - 工具名（Name）
        - MCP服务（MCP Service）
        - 描述（Description）
        - 输入参数（Input Parameters，JSON Schema）
        - 输出信息（Output）
        """
        try:
            # 1) 名称、描述
            name = getattr(tool, 'name', '') or '未知工具'
            desc = getattr(tool, 'description', '') or '无'

            # 2) 输入参数（JSON Schema）
            schema_json_str = "{}"
            try:
                openai_tool = convert_to_openai_tool(tool, strict=True)
                schema = openai_tool.get('function', {}).get('parameters') or {}
                schema_json_str = json.dumps(schema, indent=2, ensure_ascii=False)
            except Exception as schema_err:
                logger.warning(f"Failed to build input schema for tool '{name}': {schema_err}")
                schema_json_str = '{"type": "object", "properties": {}, "description": "未提供参数模式"}'

            # 3) 输出信息（无法从 BaseTool 获取严格的返回 Schema，因此给出友好提示）
            output_info = "未提供明确的输出模式；通常返回文本或 JSON 字符串，具体以工具实现为准。"
            # 尝试探测常见字段
            for attr in ("output_schema", "return_schema", "response_schema", "response_model"):
                try:
                    candidate = getattr(tool, attr, None)
                    if candidate is None:
                        continue
                    # 尝试序列化
                    if hasattr(candidate, 'model_json_schema'):
                        output_info = json.dumps(candidate.model_json_schema(), indent=2, ensure_ascii=False)
                        break
                    if hasattr(candidate, 'schema'):
                        output_info = json.dumps(candidate.schema(), indent=2, ensure_ascii=False)
                        break
                    if hasattr(candidate, 'json'):
                        output_info = candidate.json(indent=2, ensure_ascii=False)
                        break
                    if isinstance(candidate, dict):
                        output_info = json.dumps(candidate, indent=2, ensure_ascii=False)
                        break
                    # 兜底
                    output_info = str(candidate)
                    break
                except Exception:
                    continue

            # 4) 拼装 Markdown
            lines: List[str] = []
            lines.append(f"##### 工具名 {name}")
            lines.append(f"**描述 (Description)**：{desc}")
            lines.append("**输入参数 (Input Parameters, JSON Schema)**：")
            lines.append("```json")
            lines.append(schema_json_str)
            lines.append("```")
            lines.append("**输出 (Output)**：")
            # 如果看起来是 JSON（以 { 或 [ 开头），就用代码块包裹，提升可读性
            trimmed = (output_info or "").strip()
            if trimmed.startswith("{") or trimmed.startswith("["):
                lines.append("```json")
                lines.append(trimmed)
                lines.append("```")
            else:
                lines.append(trimmed or "无")

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Failed to format tool {getattr(tool, 'name', '')}: {e}")
            return (
                f"##### {getattr(tool, 'name', '未知工具')}"

                f"**描述 (Description)**：{getattr(tool, 'description', '无')}"

                f"**输入参数 (Input Parameters)**：生成失败"

                f"**输出 (Output)**：生成失败"
            )

