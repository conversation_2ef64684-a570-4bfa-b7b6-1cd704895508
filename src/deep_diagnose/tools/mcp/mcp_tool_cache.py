"""
MCP缓存管理器

职责：
- 管理MCP工具的Redis缓存
- 从MCP服务器获取工具
- 提供缓存刷新功能
"""

import json
import logging
from typing import Dict, List, Optional

from langchain_core.tools import BaseTool

from deep_diagnose.common.config import get_config
from deep_diagnose.common.utils.langchain_tool_utils import structured_tool_2_dict, dict_2_structured_tool
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.tools.mcp.mcp_tool_client import MCPToolClient
from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig

logger = logging.getLogger(__name__)


class MCPCacheManager:
    """MCP工具缓存管理器"""
    
    # 缓存相关常量
    REDIS_KEY_TEMPLATE = "{env}_deep_diagnose_mcp_tools"
    
    def __init__(self):
        """初始化缓存管理器"""
        self.mcp_tool_config = MCPToolConfig()
        self.mcp_tool_client = MCPToolClient()
        self.redis_key = self.REDIS_KEY_TEMPLATE.format(env=get_config().app_env)
        self._redis_client = None
    
    @property
    def redis_client(self) -> Optional[RedisClient]:
        """获取Redis客户端"""
        if self._redis_client is None:
            try:
                self._redis_client = RedisClient()
            except Exception as e:
                logger.error(f"Failed to initialize Redis client: {e}")
                return None
        return self._redis_client
    
    async def get_tools(self) -> Dict[str, List[BaseTool]]:
        """
        获取MCP工具，根据配置决定是否使用缓存
        
        Returns:
            Dict: {server_name: [tools]}
        """
        logger.info("Getting MCP tools")
        
        # 检查是否启用缓存
        enable_cache = get_config().get('mcp.enable_cache', True)  # 默认启用缓存
        logger.info(f"MCP cache enabled: {enable_cache}")
        
        if not enable_cache:
            # 缓存未启用，直接从服务器获取
            logger.info("Cache disabled, fetching directly from servers")
            return await self.refresh_tools()
        
        # 尝试从缓存加载
        cached_tools = self._load_from_cache()
        if cached_tools is not None:
            logger.info("Loaded MCP tools from cache")
            return cached_tools
        
        # 缓存失效，重新获取
        logger.info("Cache miss, fetching from servers")
        return await self.refresh_tools()
    
    async def refresh_tools(self) -> Dict[str, List[BaseTool]]:
        """
        刷新所有服务器的工具
        
        Returns:
            Dict: {server_name: [tools]}
        """
        logger.info("Refreshing MCP tools from all servers")
        
        try:
            results = await self.mcp_tool_client.get_all_tools()
            enabled_tools_map = self.mcp_tool_config.get_enabled_tools_map()
            tools_dict = {}
            
            for server_name, tools in results:
                # 防护检查：确保tools不为None
                if tools is None:
                    logger.warning(f"Tools from server {server_name} is None during refresh, using empty list")
                    tools = []
                
                enabled_tools = []
                # 安全地获取该服务器的启用工具列表
                server_enabled_tools = enabled_tools_map.get(server_name, [])
                
                for tool in tools:
                    if tool.name in server_enabled_tools:
                        enabled_tools.append(tool)
                
                tools_dict[server_name] = enabled_tools
                logger.info(f"Server '{server_name}': {len(enabled_tools)}/{len(tools)} tools enabled")
            
            if tools_dict:
                # 检查是否启用缓存
                enable_cache = get_config().get('mcp.enable_cache', True)
                if enable_cache:
                    self._save_to_cache(tools_dict)
                    logger.info("Tools saved to cache")
                else:
                    logger.info("Cache disabled, skipping cache save")
            return tools_dict
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Failed to refresh tools: {e}\nTraceback: {error_traceback}")
            return {}
    
    def _load_from_cache(self) -> Optional[Dict[str, List[BaseTool]]]:
        """从Redis缓存加载工具"""
        # 检查是否启用缓存
        enable_cache = get_config().get('mcp.enable_cache', True)
        if not enable_cache:
            logger.info("Cache disabled, skipping cache load")
            return None
            
        if not self.redis_client:
            return None
        
        try:
            cached_data = self.redis_client.get_cache(self.redis_key)
            if not cached_data:
                logger.info("No cached MCP tools found")
                return None
            
            tools_data = json.loads(cached_data)
            result = {}
            
            for server_name, server_data in tools_data.items():
                mcp_server = server_data.get("mcp_server")
                tools_list = server_data.get("tools", [])
                
                logger.info(f"Deserializing {len(tools_list)} tools for {server_name}")
                
                deserialized_tools = []
                for tool_dict in tools_list:
                    try:
                        tool = dict_2_structured_tool(tool_dict, mcp_server)
                        deserialized_tools.append(tool)
                    except Exception as e:
                        logger.error(f"Failed to deserialize tool {tool_dict.get('name', 'unknown')}: {e}")
                
                result[server_name] = deserialized_tools
                logger.info(f"Successfully deserialized {len(deserialized_tools)} tools for {server_name}")
            
            # 添加详细的缓存加载日志
            total_tools = sum(len(tools or []) for tools in result.values())
            logger.info(f"Loaded tools from {len(result)} servers from cache, total tools: {total_tools}")
            
            # 详细记录每个服务器的工具数量
            for server_name, tools in result.items():
                # 防护检查：确保tools不为None
                if tools is None:
                    logger.warning(f"Found None tools for server {server_name} in cache, replacing with empty list")
                    tools = []
                    result[server_name] = tools
                logger.info(f"Cache loaded - {server_name}: {len(tools)} tools")
                if tools:
                    tool_names = [tool.name for tool in tools[:3]]
                    logger.info(f"  Sample tools: {tool_names}")
                else:
                    logger.warning(f"  No tools found for {server_name} in cache")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to load from cache: {e}")
            return None
    
    def _save_to_cache(self, tools_dict: Dict[str, List[BaseTool]]) -> bool:
        """保存工具到Redis缓存"""
        # 检查是否启用缓存
        enable_cache = get_config().get('mcp.enable_cache', True)
        if not enable_cache:
            logger.info("Cache disabled, skipping cache save")
            return False
            
        if not self.redis_client:
            return False
        
        try:
            server_configs = self.mcp_tool_config.get_server_configs()
            servers = server_configs.get("servers", {})
            
            cache_data = {}
            for server_name, tools in tools_dict.items():
                tool_dicts = [structured_tool_2_dict(tool) for tool in tools]
                cache_data[server_name] = {
                    "mcp_server": servers.get(server_name),
                    "tools": tool_dicts
                }
                logger.info(f"Caching {len(tools)} tools for {server_name}, serialized to {len(tool_dicts)} dicts")
            
            cache_json = json.dumps(cache_data, ensure_ascii=False)
            cache_time = int(get_config().infrastructure.redis.refresh_time)
            
            self.redis_client.set_cache(self.redis_key, cache_json, cache_time)
            logger.info(f"Cached tools for {len(cache_data)} servers")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save to cache: {e}")
            return False
