"""
用户健康检查工具模块 - 客户全量体检服务

本模块提供阿里云ECS客户的全量健康检查功能，通过调用底层MCP工具对指定客户的所有VM实例
进行综合健康诊断，包括异常检测、告警分析和处置建议。

## 核心功能
- 🔍 **全量体检**: 对客户UID下所有VM实例进行健康探测
- ⚠️ **异常告警**: 识别网络、存储、硬件、CPU等各类异常
- 📊 **异常度分析**: 评估异常严重程度和影响范围  
- 💡 **处置建议**: 提供针对性的问题解决方案
- 📅 **时间范围**: 支持最大2天时间范围内的历史数据分析

## 适用场景
1. **定期健康巡检**: 为重要客户提供定期的系统健康评估
2. **故障排查**: 当客户报告问题时，快速进行全面诊断
3. **预防性维护**: 提前发现潜在问题，避免业务中断
4. **合规审计**: 为客户提供系统健康状况的详细报告

## 技术架构
- 基于MCP (Model Context Protocol) 工具集成
- 异步调用模式，支持长时间运行的诊断任务
- 自动时间范围计算和格式化
- 完整的错误处理和日志记录

## 返回数据结构
每个检测到的异常包含以下信息：
- exceptionName: 异常名称（技术标识）
- desc: 异常描述（中文说明）
- additionalInfo: 异常相关补充信息
- exceptionTime: 异常发生时间
- exceptionType: 异常类型（网络/存储/硬件/CPU等）
- featureName: 异常对应的特征信息
- instanceType: 实例类型
- machineId: 告警的VM实例ID
- message: 告警详细信息和解释
- ncIp: VM所在宿主机IP地址
- reason: 告警产生的根本原因
- supportTeams: 相关问题的技术支持团队
- warningLevel: 告警级别（严重程度）
- warningValue: 告警的具体数值

## 使用限制
- 时间范围最大支持2天
- 需要有效的阿里云客户UID
- 需要相应的权限访问客户资源
- 诊断过程可能需要较长时间（通常几分钟到十几分钟）

## 示例用法
```python
# 基础用法 - 检查最近2天
result = await check_customer_health("1781574661016173")

# 指定时间范围 - 检查最近1天  
result = await check_customer_health("1781574661016173", days=1)

# 处理结果
if result:
    print(f"发现 {len(result)} 个异常")
    for issue in result:
        print(f"实例 {issue['machineId']}: {issue['desc']}")
else:
    print("健康检查失败或无异常发现")
```
"""

import logging
from typing import Optional, Dict, Any
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


async def check_customer_health(
    ali_uid: str, 
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    执行客户全量健康检查 - 阿里云ECS实例综合诊断服务
    
    本函数为指定阿里云客户执行全面的健康体检，对其名下所有VM实例进行深度诊断，
    识别各类潜在问题并提供专业的处置建议。适用于故障排查、预防性维护和定期巡检。
    
    ## 功能详述
    
    ### 检查范围
    - **全实例覆盖**: 自动发现并检查客户UID下的所有ECS实例
    - **多维度诊断**: 涵盖网络、存储、硬件、CPU、内存等各个层面
    - **历史数据分析**: 基于指定时间范围内的监控数据和日志进行分析
    - **实时状态评估**: 结合当前实例状态和历史趋势进行综合判断
    
    ### 异常检测类型
    - **网络异常**: 网络连接问题、带宽瓶颈、丢包率异常等
    - **存储异常**: 磁盘I/O性能问题、存储空间不足、读写错误等  
    - **硬件异常**: CPU过载、内存不足、硬件故障等
    - **系统异常**: 操作系统级别的问题、服务异常等
    - **性能异常**: 响应时间过长、吞吐量下降等
    
    ### 输出信息
    返回结构化的异常列表，每个异常包含：
    - **基础信息**: 异常名称、描述、发生时间、影响实例
    - **技术细节**: 异常类型、特征信息、具体数值、根本原因
    - **定位信息**: 实例ID、宿主机IP、实例类型
    - **处置指导**: 告警级别、支持团队、解决建议
    
    ## 参数说明
    
    Args:
        ali_uid (str): 阿里云客户的唯一标识符
            - 格式: 纯数字字符串，如 "1781574661016173"
            - 来源: 可从阿里云控制台、API或客户信息中获取
            - 权限: 需要有访问该客户资源的相应权限
            
        start_time (Optional[str]): 检查开始时间
            - 格式: "yyyy-MM-dd HH:mm:ss"，如 "2025-08-26 00:00:00"
            - 默认: None（使用工具默认时间范围，通常为最近2天）
            - 注意: 与end_time配合使用，定义检查的时间窗口
            
        end_time (Optional[str]): 检查结束时间
            - 格式: "yyyy-MM-dd HH:mm:ss"，如 "2025-08-28 23:59:59"
            - 默认: None（使用工具默认时间范围）
            - 注意: 必须晚于start_time，时间跨度不超过2天
    
    ## 返回值说明
    
    Returns:
        Optional[List[Dict[str, Any]]]: 健康检查结果
        
        成功时返回异常列表，每个异常字典包含：
        ```python
        {
            "exceptionName": "NetworkLatencyHigh",      # 异常技术名称
            "desc": "网络延迟过高",                      # 中文描述
            "additionalInfo": "平均延迟120ms",          # 补充信息
            "exceptionTime": "2025-08-27 14:30:00",    # 异常时间
            "exceptionType": "网络",                     # 异常分类
            "featureName": "网络延迟监控",               # 特征名称
            "instanceType": "ecs.c6.large",            # 实例规格
            "level": 0,                                 # 保留字段
            "machineId": "i-bp1234567890abcdef",       # 实例ID
            "message": "实例网络延迟超过阈值...",        # 详细说明
            "ncIp": "*************",                   # 宿主机IP
            "reason": "网络拥塞导致延迟增加",            # 根本原因
            "supportTeams": ["网络团队", "运维团队"],    # 支持团队
            "warningLevel": "高",                       # 告警级别
            "warningValue": "120ms"                     # 告警数值
        }
        ```
        
        失败时返回 None，可能的失败原因：
        - 客户UID不存在或无权限访问
        - 时间格式错误或时间范围无效
        - 网络连接问题
        - 底层诊断服务异常
    
    ## 使用示例
    
    ### 基础用法（使用默认时间范围）
    ```python
    # 检查指定客户，使用默认时间范围（最近2天）
    result = await check_customer_health("1781574661016173")
    
    if result:
        print(f"检测到 {len(result)} 个异常")
        for issue in result:
            print(f"[{issue['warningLevel']}] {issue['desc']} - 实例: {issue['machineId']}")
    else:
        print("检查失败或系统健康")
    ```
    
    ### 指定时间范围
    ```python
    # 检查指定时间段的健康状况
    start = "2025-08-27 00:00:00"
    end = "2025-08-27 23:59:59"
    result = await check_customer_health("1781574661016173", start, end)
    
    if result:
        print(f"在 {start} 到 {end} 期间检测到 {len(result)} 个异常")
    ```
    
    ### 高级用法 - 异常分析
    ```python
    # 按异常类型分组统计
    result = await check_customer_health("1781574661016173")
    
    if result:
        from collections import defaultdict
        by_type = defaultdict(list)
        by_level = defaultdict(int)
        
        for issue in result:
            by_type[issue['exceptionType']].append(issue)
            by_level[issue['warningLevel']] += 1
        
        print("=== 异常类型统计 ===")
        for exc_type, issues in by_type.items():
            print(f"{exc_type}: {len(issues)}个")
            for issue in issues[:3]:  # 显示前3个
                print(f"  - {issue['desc']} ({issue['machineId']})")
        
        print("\n=== 告警级别统计 ===")
        for level, count in by_level.items():
            print(f"{level}级告警: {count}个")
    ```
    
    ### 错误处理和重试
    ```python
    import asyncio
    
    async def robust_health_check(ali_uid: str, max_retries: int = 3):
        for attempt in range(max_retries):
            try:
                result = await check_customer_health(ali_uid)
                if result is not None:
                    return result
                print(f"尝试 {attempt + 1} 失败，结果为空")
            except Exception as e:
                print(f"尝试 {attempt + 1} 异常: {e}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(5)  # 等待5秒后重试
        
        return None
    ```
    
    ## 注意事项
    
    ### 性能考虑
    - **执行时间**: 通常需要2-10分钟，取决于实例数量和检查深度
    - **资源消耗**: 对目标实例性能影响极小，主要消耗诊断服务资源
    - **并发限制**: 建议避免对同一客户并发执行多次检查
    - **时间范围**: 较大的时间范围会增加执行时间，建议根据需要选择
    
    ### 权限要求
    - 需要有访问指定客户资源的权限
    - 需要调用底层MCP诊断工具的权限
    - 建议在授权的运维环境中使用
    
    ### 数据安全
    - 返回的数据包含客户敏感信息，请妥善处理
    - 遵循数据保护和隐私相关的法规要求
    - 建议对结果进行适当的脱敏处理
    
    ### 时间参数使用
    - 如果只提供start_time或end_time中的一个，建议两个都提供
    - 时间格式必须严格遵循 "yyyy-MM-dd HH:mm:ss"
    - 时间范围不能超过2天（底层工具限制）
    - 建议使用UTC时间或明确时区
    
    ## 故障排除
    
    ### 常见问题
    1. **返回None**: 
       - 检查客户UID格式是否正确
       - 确认权限配置
       - 检查网络连接
       - 验证时间格式和范围
    
    2. **执行超时**: 
       - 可能是实例数量过多，建议缩小时间范围
       - 检查网络连接稳定性
       - 联系技术支持确认服务状态
    
    3. **权限错误**: 
       - 确认有访问目标客户资源的相应权限
       - 检查MCP工具配置和权限
    
    4. **时间格式错误**:
       - 确保时间格式为 "yyyy-MM-dd HH:mm:ss"
       - 检查时间范围是否合理（不超过2天）
       - 确保end_time晚于start_time
    
    ### 日志查看
    函数执行过程中会输出详细日志，包括：
    - 参数验证和时间计算结果
    - MCP工具加载状态
    - 执行进度信息
    - 错误详情和完整堆栈
    
    可通过配置日志级别为DEBUG获取更多调试信息：
    ```python
    import logging
    logging.getLogger('deep_diagnose.tools.utils.user_health_check').setLevel(logging.DEBUG)
    ```
    """
    try:
        from datetime import datetime, timedelta
        
        # 参数验证和时间计算
        if start_time is not None and end_time is not None:
            # 使用指定的开始和结束时间
            start_time_str = start_time
            end_time_str = end_time
            logger.info(f"使用指定的时间范围: {start_time_str} 到 {end_time_str}")
        else:
            # 使用默认时间范围，传递None让工具使用默认值
            start_time_str = None
            end_time_str = None
            logger.info("使用工具默认时间范围（最近2天）")
        
        # 获取MCP工具管理器
        tool_manager = MCPToolManager()
        
        # 获取所有可用的MCP工具
        tools = await tool_manager.get_enabled_mcp_tools()
        
        # 查找checkHealthForCustomerVms工具
        check_health_tool = None
        for tool in tools:
            if tool.name == "checkHealthForCustomerVms":
                check_health_tool = tool
                break
        
        if check_health_tool is None:
            logger.error("checkHealthForCustomerVms工具未找到")
            return None
        
        # 准备调用参数（使用正确的参数名）
        tool_input = {
            "aliUid": ali_uid,  # 注意：使用camelCase
            "startTime": start_time_str,
            "endTime": end_time_str
        }
        
        logger.info(f"开始检查客户 {ali_uid} 的健康状态")
        logger.info(f"时间范围: {start_time_str} 到 {end_time_str}")
        
        # 调用工具
        result = await check_health_tool.ainvoke(tool_input)
        
        logger.info(f"客户 {ali_uid} 健康检查完成")
        return result
        
    except Exception as e:
        logger.error(f"检查客户健康状态时发生错误: {e}")
        import traceback
        logger.error(f"完整错误信息: {traceback.format_exc()}")
        return None


# 为了兼容原始函数名，提供别名
check_customer_healther = check_customer_health


def get_interface_capabilities() -> str:
    """
    获取用户健康检查接口的能力描述 - 供大模型理解和使用
    
    本函数返回详细的接口能力说明，帮助大模型理解如何正确使用客户健康检查功能。
    
    Returns:
        str: 接口能力的详细描述文档
    """
    return """
# 客户健康检查接口能力说明

## 接口概述
`check_customer_health` 是一个强大的阿里云ECS客户健康诊断接口，能够对指定客户的所有VM实例进行全面的健康检查和异常诊断。

## 核心能力

### 1. 全量实例检查
- 自动发现客户UID下的所有ECS实例
- 无需手动指定实例列表
- 支持不同规格和类型的实例

### 2. 多维度健康诊断
- **网络层面**: 延迟、丢包、带宽、连通性
- **存储层面**: 磁盘I/O、存储空间、读写性能
- **计算层面**: CPU使用率、内存占用、负载情况
- **系统层面**: 操作系统状态、服务运行情况
- **硬件层面**: 硬件故障、温度异常等

### 3. 智能异常识别
- 基于历史数据和实时监控进行异常检测
- 支持阈值告警和趋势分析
- 提供异常严重程度评估
- 自动关联相关异常事件

### 4. 专业处置建议
- 针对每个异常提供具体的解决方案
- 标识负责的技术支持团队
- 提供操作优先级和风险评估
- 包含预防措施和最佳实践建议

## 接口签名
```python
async def check_customer_health(
    ali_uid: str, 
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> Optional[List[Dict[str, Any]]]
```

## 参数详解

### ali_uid (必需)
- **类型**: 字符串
- **格式**: 纯数字，如 "1781574661016173"
- **获取方式**: 
  - 阿里云控制台用户信息
  - 客户工单系统
  - 用户管理API
- **验证**: 必须是有效的阿里云客户UID

### start_time (可选)
- **类型**: 字符串或None
- **格式**: "yyyy-MM-dd HH:mm:ss"
- **示例**: "2025-08-27 00:00:00"
- **默认**: None（使用系统默认时间范围）
- **用途**: 定义检查的开始时间点

### end_time (可选)
- **类型**: 字符串或None
- **格式**: "yyyy-MM-dd HH:mm:ss"
- **示例**: "2025-08-27 23:59:59"
- **默认**: None（使用系统默认时间范围）
- **限制**: 必须晚于start_time，时间跨度≤2天

## 返回数据结构

### 基础健康检查返回格式
返回异常列表 `List[Dict[str, Any]]`，每个异常包含：

```python
{
    # 基础标识信息
    "exceptionName": "NetworkLatencyHigh",           # 异常技术名称
    "desc": "网络延迟过高",                          # 中文描述
    "exceptionTime": "2025-08-27 14:30:00",        # 发生时间
    
    # 分类和特征
    "exceptionType": "网络",                         # 异常类型
    "featureName": "网络延迟监控",                   # 监控特征
    
    # 实例信息
    "machineId": "i-bp1234567890abcdef",           # ECS实例ID
    "instanceType": "ecs.c6.large",               # 实例规格
    "ncIp": "*************",                      # 宿主机IP
    
    # 详细信息
    "message": "实例网络延迟超过阈值，影响业务响应",   # 详细说明
    "reason": "网络拥塞导致延迟增加",                # 根本原因
    "additionalInfo": "平均延迟120ms，峰值180ms",   # 补充信息
    
    # 告警信息
    "warningLevel": "高",                          # 告警级别
    "warningValue": "120ms",                       # 具体数值
    
    # 支持信息
    "supportTeams": ["网络团队", "运维团队"],        # 负责团队
    "level": 0                                     # 保留字段
}
```

### 失败返回
返回 `None`，常见失败原因：
- 客户UID无效或无权限
- 时间参数格式错误
- 网络连接问题
- 服务暂时不可用

## 使用场景和示例

### 场景1: 日常健康巡检
```python
# 使用默认时间范围检查客户健康状况
result = await check_customer_health("1781574661016173")

if result:
    print(f"发现 {len(result)} 个需要关注的问题")
    
    # 按严重程度分类
    critical = [x for x in result if x['warningLevel'] == '严重']
    high = [x for x in result if x['warningLevel'] == '高']
    medium = [x for x in result if x['warningLevel'] == '中']
    
    print(f"严重问题: {len(critical)}个")
    print(f"高级告警: {len(high)}个") 
    print(f"中级告警: {len(medium)}个")
else:
    print("客户系统整体健康，未发现异常")
```

### 场景2: 故障排查
```python
# 针对特定时间段进行详细检查
incident_start = "2025-08-27 10:00:00"
incident_end = "2025-08-27 12:00:00"

result = await check_customer_health(
    "1781574661016173", 
    incident_start, 
    incident_end
)

if result:
    print(f"在故障时间段 {incident_start} - {incident_end} 发现:")
    
    # 按实例分组
    by_instance = {}
    for issue in result:
        instance_id = issue['machineId']
        if instance_id not in by_instance:
            by_instance[instance_id] = []
        by_instance[instance_id].append(issue)
    
    for instance_id, issues in by_instance.items():
        print(f"\n实例 {instance_id}:")
        for issue in issues:
            print(f"  - [{issue['exceptionTime']}] {issue['desc']}")
            print(f"    原因: {issue['reason']}")
            print(f"    建议联系: {', '.join(issue['supportTeams'])}")
```

### 场景3: 预防性维护
```python
# 识别潜在风险，制定维护计划
result = await check_customer_health("1781574661016173")

if result:
    # 按异常类型统计
    type_stats = {}
    for issue in result:
        exc_type = issue['exceptionType']
        if exc_type not in type_stats:
            type_stats[exc_type] = {'count': 0, 'instances': set()}
        
        type_stats[exc_type]['count'] += 1
        type_stats[exc_type]['instances'].add(issue['machineId'])
    
    print("=== 维护建议 ===")
    for exc_type, stats in type_stats.items():
        affected_count = len(stats['instances'])
        print(f"{exc_type}问题: {stats['count']}个告警，影响{affected_count}个实例")
        
        if exc_type == "存储":
            print("  建议: 安排存储扩容或I/O优化")
        elif exc_type == "网络":
            print("  建议: 检查网络配置和带宽规划")
        elif exc_type == "硬件":
            print("  建议: 安排硬件检查和更换计划")
```

### 场景4: 报告生成
```python
# 生成客户健康报告
result = await check_customer_health("1781574661016173")

def generate_health_report(customer_uid: str, issues: List[Dict]) -> str:
    if not issues:
        return f"客户 {customer_uid} 系统健康，未发现异常。"
    
    report = f"# 客户 {customer_uid} 健康检查报告\\n\\n"
    report += f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n"
    report += f"发现问题总数: {len(issues)}\\n\\n"
    
    # 按严重程度统计
    severity_count = {}
    for issue in issues:
        level = issue['warningLevel']
        severity_count[level] = severity_count.get(level, 0) + 1
    
    report += "## 问题严重程度分布\\n"
    for level, count in severity_count.items():
        report += f"- {level}级: {count}个\\n"
    
    report += "\\n## 详细问题列表\\n"
    for i, issue in enumerate(issues, 1):
        report += f"### 问题 {i}: {issue['desc']}\\n"
        report += f"- **实例**: {issue['machineId']}\\n"
        report += f"- **时间**: {issue['exceptionTime']}\\n"
        report += f"- **类型**: {issue['exceptionType']}\\n"
        report += f"- **级别**: {issue['warningLevel']}\\n"
        report += f"- **原因**: {issue['reason']}\\n"
        report += f"- **负责团队**: {', '.join(issue['supportTeams'])}\\n\\n"
    
    return report

# 生成并保存报告
if result:
    report = generate_health_report("1781574661016173", result)
    print(report)
```

## 最佳实践

### 1. 时间范围选择
- **日常巡检**: 使用默认时间范围（最近2天）
- **故障排查**: 精确指定故障发生的时间段
- **趋势分析**: 使用最大时间范围（2天）获取更多数据

### 2. 结果处理
- 始终检查返回值是否为None
- 按业务需要对异常进行分类和优先级排序
- 关注高级别告警，优先处理严重问题

### 3. 性能优化
- 避免频繁调用，建议间隔至少10分钟
- 对于大客户，考虑分时段检查
- 在业务低峰期执行全面检查

### 4. 错误处理
- 实现重试机制处理临时性失败
- 记录详细日志便于问题排查
- 设置合理的超时时间

### 5. 数据安全
- 对敏感信息进行脱敏处理
- 限制结果数据的访问权限
- 遵循数据保留和删除策略

## 限制和注意事项

### 技术限制
- 时间范围最大2天
- 执行时间可能较长（2-10分钟）
- 需要相应的权限和网络访问

### 业务限制
- 仅支持阿里云ECS实例
- 需要客户授权访问其资源
- 某些专有网络可能有访问限制

### 使用建议
- 在生产环境使用前先在测试环境验证
- 建立监控和告警机制跟踪接口可用性
- 定期更新和维护相关权限配置

通过以上详细说明，大模型可以充分理解和正确使用客户健康检查接口，为用户提供专业的ECS实例健康诊断服务。
"""


async def main():
    """
    主函数 - 测试客户健康检查功能
    """
    import asyncio
    
    # 测试客户ID
    test_ali_uid = "1781574661016173"
    
    logger.info("=" * 60)
    logger.info("开始测试客户健康检查功能")
    logger.info(f"客户ID: {test_ali_uid}")
    logger.info("使用默认时间范围")
    logger.info("=" * 60)
    
    try:
        # 异步调用
        result = await check_customer_health(test_ali_uid)
        
        if result is not None:
            logger.info("✅ 健康检查成功完成!")
            logger.info(f"结果类型: {type(result)}")
            logger.info("=" * 50)
            logger.info("📋 健康检查结果:")
            logger.info("=" * 50)
            logger.info(f"{result}")
            logger.info("=" * 50)
            
            # 如果结果是字符串，显示统计信息
            if isinstance(result, str):
                logger.info(f"📊 结果统计:")
                logger.info(f"   字符总数: {len(result)}")
                logger.info(f"   行数: {result.count(chr(10)) + 1}")
                logger.info(f"   是否为空: {'是' if len(result.strip()) == 0 else '否'}")
            elif isinstance(result, list):
                logger.info(f"📊 结果统计:")
                logger.info(f"   检测到的问题数量: {len(result)}")
                for i, item in enumerate(result[:3]):  # 显示前3个问题
                    logger.info(f"   问题 {i+1}: {item.get('exceptionName', '未知异常')}")
        else:
            logger.warning("⚠️ 健康检查返回空结果")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(f"完整错误信息: {traceback.format_exc()}")
        return False
    
    logger.info("=" * 60)
    logger.info("🎉 测试完成!")
    logger.info("=" * 60)
    
    return True


if __name__ == "__main__":
    import asyncio
    import logging
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    asyncio.run(main())