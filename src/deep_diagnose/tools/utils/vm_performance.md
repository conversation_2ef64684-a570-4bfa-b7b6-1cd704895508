# VM Performance Tool

## 概述

VM性能指标查询工具，获取阿里云ECS实例的性能监控数据。

## 支持的指标

- **CPU**: `VmStealMetric/vcpucpu` (使用率), `VmStealMetric/vmsteal` (争抢)
- **存储**: `VmStorageIOLatency/read_lat_ms` (读延迟), `VmStorageIOLatency/write_lat_ms` (写延迟), `VmStorageIOLatency/read_iops` (读IOPS), `VmStorageIOLatency/write_iops` (写IOPS)
- **网络**: `VmPpsBps/tx_pps` (出PPS), `VmPpsBps/rx_pps` (入PPS), `VmVportDropMetric/drop_ratio` (丢包率)
- **内存**: `VmMemBWMetric/memory_bw` (带宽)

## API

### `list_vm_performance_metrics(instance_id, metrics=None, start_time=None, end_time=None)`

**参数**:
- `instance_id` (str): VM实例ID
- `metrics` (list, 可选): 指标列表，默认使用常用指标
- `start_time` (str, 可选): 开始时间 "yyyy-MM-dd HH:mm:ss"
- `end_time` (str, 可选): 结束时间 "yyyy-MM-dd HH:mm:ss"

**返回**: 字典格式的时间序列数据 `{"指标名": {"时间戳": 数值, ...}, ...}`

## 使用示例

```python
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics

# 基本用法
result = await list_vm_performance_metrics("eci-0xi16p1femf8dbmxdqpp")

# 自定义指标
result = await list_vm_performance_metrics(
    "eci-0xi16p1femf8dbmxdqpp", 
    metrics=["VmStealMetric/vcpucpu", "VmMemBWMetric/memory_bw"]
)

# 指定时间范围
result = await list_vm_performance_metrics(
    "eci-0xi16p1femf8dbmxdqpp",
    start_time="2024-08-28 00:00:00",
    end_time="2024-08-28 02:00:00"
)
```

## 注意事项

- 默认时间范围：最近2小时
- 最大时间范围：6小时
- 时间格式：`yyyy-MM-dd HH:mm:ss`

