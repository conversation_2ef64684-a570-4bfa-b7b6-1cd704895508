"""
VM性能指标工具模块

提供VM实例性能诊断功能
"""

import logging
import json
import re
from typing import Optional, Dict, Any, List
from datetime import datetime
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


def _process_vm_performance_data(raw_data: Any) -> Dict[str, List[Dict[str, Any]]]:
    """处理VM性能数据为标准时间序列格式"""
    if not raw_data:
        return {}
    
    # 处理字符串JSON
    if isinstance(raw_data, str):
        try:
            raw_data = json.loads(raw_data)
        except json.JSONDecodeError:
            # 修复时间戳键的引号问题
            try:
                fixed_json = re.sub(r'(\d+):', r'"\1":', raw_data)
                raw_data = json.loads(fixed_json)
                logger.info("JSON格式修复成功")
            except:
                logger.error("JSON解析失败")
                return {}
    
    if not isinstance(raw_data, dict):
        logger.warning(f"数据类型不正确，期望dict，实际: {type(raw_data)}")
        return {}
    
    # 转换为标准时间序列格式
    result = {}
    for metric_name, metric_data in raw_data.items():
        if not isinstance(metric_data, dict):
            logger.warning(f"跳过非字典格式的指标: {metric_name}")
            continue
            
        time_series = []
        for timestamp, value in metric_data.items():
            try:
                ts_int = int(timestamp)
                val_float = float(value)
                time_series.append({
                    "timestamp": ts_int,
                    "datetime": datetime.fromtimestamp(ts_int).strftime("%Y-%m-%d %H:%M:%S"),
                    "value": val_float
                })
            except (ValueError, TypeError):
                continue
        
        if time_series:
            time_series.sort(key=lambda x: x["timestamp"])
            result[metric_name] = time_series
            logger.info(f"处理指标 '{metric_name}': {len(time_series)} 个数据点")
    
    return result


def _format_performance_result(result: Any) -> None:
    """
    格式化性能指标结果输出
    
    Args:
        result: 性能指标查询结果
    """
    import json
    from datetime import datetime
    
    if isinstance(result, dict):
        logger.info("📊 性能指标数据概览:")
        
        # 统计信息
        total_metrics = len(result)
        logger.info(f"   总指标数量: {total_metrics}")
        
        # 按指标类型分组显示
        for metric_name, metric_data in result.items():
            if isinstance(metric_data, dict):
                data_points = len(metric_data)
                logger.info(f"\n🔍 指标: {metric_name}")
                logger.info(f"   数据点数量: {data_points}")
                
                if data_points > 0:
                    # 获取时间范围
                    timestamps = list(metric_data.keys())
                    values = list(metric_data.values())
                    
                    # 转换时间戳为可读格式
                    try:
                        start_time = datetime.fromtimestamp(int(min(timestamps)))
                        end_time = datetime.fromtimestamp(int(max(timestamps)))
                        logger.info(f"   时间范围: {start_time.strftime('%Y-%m-%d %H:%M:%S')} ~ {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    except (ValueError, TypeError):
                        logger.info(f"   时间范围: {min(timestamps)} ~ {max(timestamps)}")
                    
                    # 统计值信息
                    numeric_values = [v for v in values if isinstance(v, (int, float))]
                    if numeric_values:
                        logger.info(f"   数值范围: {min(numeric_values):.4f} ~ {max(numeric_values):.4f}")
                        logger.info(f"   平均值: {sum(numeric_values)/len(numeric_values):.4f}")
                    
                    # 显示最近几个数据点
                    logger.info("   最近数据点:")
                    sorted_items = sorted(metric_data.items(), key=lambda x: int(x[0]), reverse=True)
                    for i, (ts, value) in enumerate(sorted_items[:3]):
                        try:
                            time_str = datetime.fromtimestamp(int(ts)).strftime('%H:%M:%S')
                        except (ValueError, TypeError):
                            time_str = str(ts)
                        logger.info(f"     {time_str}: {value}")
                        
        logger.info("\n💾 原始数据 (JSON格式):")
        # 格式化JSON输出，使其更易读
        formatted_json = json.dumps(result, indent=2, ensure_ascii=False)
        for line in formatted_json.split('\n')[:20]:  # 只显示前20行
            logger.info(f"   {line}")
        if len(formatted_json.split('\n')) > 20:
            logger.info("   ... (数据过长，已截断)")
            
    elif isinstance(result, str):
        logger.info("📄 字符串结果:")
        logger.info(f"   长度: {len(result)} 字符")
        if len(result) <= 500:
            logger.info(f"   内容: {result}")
        else:
            logger.info(f"   内容预览: {result[:500]}...")
    else:
        logger.info(f"📋 其他类型结果: {type(result)}")
        logger.info(f"   内容: {str(result)[:500]}")


async def list_vm_performance_metrics(
    instance_id: str, 
    metrics: Optional[list] = None,
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """
    获取VM实例性能指标趋势数据
    
    Args:
        instance_id: VM实例ID
        metrics: 需要查询的性能指标列表，如果为None则使用默认指标
        start_time: 开始时间，格式为 "yyyy-MM-dd HH:mm:ss"，如果为None则使用默认时间
        end_time: 结束时间，格式为 "yyyy-MM-dd HH:mm:ss"，如果为None则使用默认时间
        
    Returns:
        Dict: 性能指标趋势数据，格式为 {"metric1": [[timestamp, value], ...], ...}
        
    Note:
        - 如果start_time和end_time都为None，工具将使用默认时间范围（最近2小时）
        - 工具最大支持6小时的时间范围
    """
    try:
        from datetime import datetime, timedelta
        
        # 参数验证和时间计算
        if start_time is not None and end_time is not None:
            # 使用指定的开始和结束时间
            start_time_str = start_time
            end_time_str = end_time
            logger.info(f"使用指定的时间范围: {start_time_str} 到 {end_time_str}")
            
            # 验证时间范围是否超过6小时限制
            try:
                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                duration = end_dt - start_dt
                if duration.total_seconds() > 6 * 3600:  # 6小时 = 6 * 3600秒
                    logger.warning(f"指定的时间范围超过6小时限制: {duration}，工具可能无法正常工作")
            except ValueError as ve:
                logger.warning(f"时间格式验证失败: {ve}")
                
        else:
            # 使用默认时间范围，传递None让工具使用默认值
            start_time_str = None
            end_time_str = None
            logger.info("使用工具默认时间范围（最近2小时）")
        
        # 如果没有指定指标，使用默认的常用指标
        if metrics is None:
            metrics = [
                "VmStealMetric/vcpucpu",  # CPU使用率
                "VmStealMetric/vmsteal",  # CPU争抢指标
                "VmStorageIOLatency/read_lat_ms",  # IO读延迟
                "VmStorageIOLatency/write_lat_ms",  # IO写延迟
                "VmStorageIOLatency/read_iops",  # 读IOPS
                "VmStorageIOLatency/write_iops",  # 写IOPS
                "VmPpsBps/tx_pps",  # 出方向网络PPS
                "VmPpsBps/rx_pps",  # 入方向网络PPS
                "VmVportDropMetric/drop_ratio",  # 网络丢包率
                "VmMemBWMetric/memory_bw"  # 内存带宽
            ]
        
        # 获取MCP工具管理器
        tool_manager = MCPToolManager()
        
        # 获取所有可用的MCP工具
        tools = await tool_manager.get_enabled_mcp_tools()
        
        # 查找listVmPerformanceMetrics工具
        performance_tool = None
        for tool in tools:
            if tool.name == "listVmPerformanceMetrics":
                performance_tool = tool
                break
        
        if performance_tool is None:
            for tool in tools:
                logger.info(f"tool: {tool.name}")

            logger.error("listVmPerformanceMetrics工具未找到")
            return None
        
        # 准备调用参数 - 处理metrics参数格式
        processed_metrics = None
        if metrics:
            # 如果metrics被包装在元组中，先解包
            if isinstance(metrics, tuple) and len(metrics) == 1:
                metrics = metrics[0]
            
            # 确保metrics是列表格式（工具期望array类型）
            if isinstance(metrics, list):
                processed_metrics = metrics
            elif isinstance(metrics, str):
                # 如果是字符串，按逗号分割为列表
                processed_metrics = [m.strip() for m in metrics.split(",")]
            else:
                logger.warning(f"不支持的metrics类型: {type(metrics)}, 使用默认指标")
                processed_metrics = None
        
        tool_input = {
            "instanceId": instance_id,
            "metrics": processed_metrics,
            "startTime": start_time_str,
            "endTime": end_time_str
        }
        
        logger.info(f"开始查询VM实例 {instance_id} 的性能指标")
        logger.info(f"时间范围: {start_time_str} 到 {end_time_str}")
        logger.info(f"原始指标参数: {metrics} (类型: {type(metrics)})")
        logger.info(f"处理后指标列表: {processed_metrics}")
        
        # 调用工具
        result = await performance_tool.ainvoke(tool_input)
        
        logger.info(f"VM实例 {instance_id} 性能指标查询完成")
        
        # 处理和标准化数据
        processed_result = _process_vm_performance_data(result)
        return processed_result
        
    except Exception as e:
        logger.error(f"获取VM性能指标时发生错误: {e}")
        import traceback
        logger.error(f"完整错误信息: {traceback.format_exc()}")
        return None


async def main():
    """
    主函数 - 测试VM性能指标功能
    """
    import asyncio
    
    # 测试VM实例ID（请替换为实际的实例ID）
    test_instance_id = "eci-0xi16p1femf8dbmxdqpp"  # 示例实例ID
    
    logger.info("=" * 60)
    logger.info("开始测试VM性能指标功能")
    logger.info(f"实例ID: {test_instance_id}")
    logger.info("使用默认时间范围")
    logger.info("=" * 60)
    
    try:
        # 异步调用
        result = await list_vm_performance_metrics(test_instance_id)
        
        if result is not None:
            logger.info("✅ 性能诊断成功完成!")
            logger.info(f"结果类型: {type(result)}")
            logger.info("=" * 50)
            logger.info("📋 性能诊断结果:")
            logger.info("=" * 50)
            _format_performance_result(result)
            logger.info("=" * 50)
            
            # 如果结果是字典，显示详细信息
            if isinstance(result, dict):
                logger.info(f"📊 结果分析:")
                problems = result.get('problems', {})
                solutions = result.get('solutions', [])
                
                if problems:
                    logger.info(f"   发现性能问题数量: {len(problems)}")
                    for problem_type, details in problems.items():
                        logger.info(f"   问题类型: {problem_type}")
                        if isinstance(details, dict):
                            for key, value in details.items():
                                logger.info(f"     {key}: {value}")
                else:
                    logger.info("   ✅ 未发现性能问题")
                
                if solutions:
                    logger.info(f"   解决方案数量: {len(solutions)}")
                    for i, solution in enumerate(solutions[:3]):  # 显示前3个解决方案
                        logger.info(f"   解决方案 {i+1}: {solution}")
                        
            elif isinstance(result, str):
                logger.info(f"📊 结果统计:")
                logger.info(f"   字符总数: {len(result)}")
                logger.info(f"   是否为空: {'是' if len(result.strip()) == 0 else '否'}")
                
        else:
            logger.warning("⚠️ 性能诊断返回空结果")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {e}")
        import traceback
        logger.error(f"完整错误信息: {traceback.format_exc()}")
        return False
    
    logger.info("=" * 60)
    logger.info("🎉 测试完成!")
    logger.info("=" * 60)
    
    return True


if __name__ == "__main__":
    import asyncio
    import logging
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    asyncio.run(main())