import asyncio
import json
from datetime import datetime

# 主应用模块导入
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data


async def main():
    print("🚀 开始执行性能数据分析...")

    # 1. 定义采集任务 (这个列表由外部传入，此处为示例)
    metrics = None
    collect_data_tasks = [
        {
            "instance_id": "i-zf50vrtyybpswo0egjxm",
            "start_time": "2025-08-30 21:00:00",
            "end_time": "2025-08-31 01:00:00",
            "metrics": metrics
        }
    ]

    request_id = "req_733c9be9-e36c-434c-b64f-c6c7e9e51296"
    if not request_id or not request_id.strip():
        request_id = "default_request"

    print(f"📋 使用请求ID: {request_id}")

    # 2. 循环执行每个独立的采集任务
    for task in collect_data_tasks:
        instance_id = task["instance_id"]
        start_time = task["start_time"]
        end_time = task["end_time"]
        task_metrics = task["metrics"]

        try:
            print(f"📊 正在执行任务: 实例 {instance_id} 从 {start_time} 到 {end_time}")

            # 2a. 获取性能数据 (函数返回值为 dict)
            performance_data = await list_vm_performance_metrics(
                instance_id=instance_id,
                metrics=task_metrics,
                start_time=start_time,
                end_time=end_time
            )

            # 2b. 检查返回是否为空
            if not performance_data:
                print(f"❌ 未获取到数据: 实例 {instance_id}, 时间 {start_time}-{end_time}")
                continue

            save_result = save_and_register_data(request_id, instance_id, performance_data)

            if save_result.get("success"):
                print(f"✅ 任务结果已保存: {save_result.get('json_file')}")
            else:
                print(f"❌ 任务结果保存失败: {save_result.get('error')}")

        except Exception as e:
            print(f"❌ 执行任务时发生严重错误 (实例 {instance_id}): {e}")

    print("✅ 所有任务执行完成！")

# 执行主函数
asyncio.run(main())