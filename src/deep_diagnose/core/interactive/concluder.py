import logging
import time

from langchain_core.tools.base import BaseTool

from deep_diagnose.common.utils import time_utils
from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.core.interactive.param_manager import ContextualParamManager
from deep_diagnose.core.interactive.state import ExecutionState
from deep_diagnose.llms.app_bailian import Bai<PERSON><PERSON><PERSON><PERSON><PERSON>, app_bailian
from deep_diagnose.tools.decorators import catch_all_async_errors

logger = logging.getLogger(__name__)


@catch_all_async_errors
async def conclude(state: ExecutionState) -> None:
    logger.info(f"Start organizing response for conclusion.")
    # 构建业务参数
    biz_params = {
        "current_time": time_utils.current_time_with_week(),
        "plan": _organize_plan(state),
        "mid_results": _organize_mid_results(state),
    }
    # 根据问题复杂度调用concluder
    start_ts = time.time()
    concluder_app = _select_concluder(state)
    responses = app_bailian.app_stream_call(app=concluder_app, prompt=state.question, biz_params=biz_params)
    # 整理输出
    context_param_manager = ContextualParamManager()
    context = state.question + biz_params['mid_results']
    async for result, reason in responses:
        if not is_blank(reason):
            state.reason_content += reason
        if not is_blank(result):
            state.raw_result_content += result
            state.result_content = context_param_manager.sanitize_content(state.raw_result_content, context)
    logger.info(f"Concluder output: {state.result_content}")
    end_ts = time.time()
    logger.info(f"Call of concluder costs {end_ts - start_ts:.3f} s")


def _select_concluder(state):
    """
    根据执行状态选择合适的concluder应用类型
    
    参数:
        state (ExecutionState): 执行状态对象，包含fast_path_mode和fast_path_scenario等属性
    
    返回:
        BailianAppEnum: 返回对应的concluder应用枚举值
    """
    if state.fast_path_scenario in ["FASTPATH_VM_DOWN@BAILIAN", "FASTPATH_NC_DOWN@BAILIAN"]:
        return BailianAppEnum.fast_concluder
    else:
        complexity = _calculate_plan_complexity(state)
        logger.info(f"Complexity of plan: {complexity}")
        if complexity <= 1:
            return BailianAppEnum.concluder_l1
        elif complexity <= 3:
            return BailianAppEnum.concluder_l2
        else:
            return BailianAppEnum.concluder_l3


def _calculate_plan_complexity(state: ExecutionState) -> float:
    """
    计算执行计划的复杂度

    参数:
        state (ExecutionState): 执行状态对象，包含step_states等属性

    返回:
        float: 执行计划的总复杂度分数
    """
    # 定义各工具的复杂度权重
    complexity_map = {
        "listOnDutyStaffs": 2,  # 获取值班人员列表的复杂度为2
        "listKnowledge": 4,  # 获取知识列表的复杂度为4
        None: 0  # 空工具的复杂度为0
    }

    # 遍历所有步骤状态，累加复杂度
    total_complexity = 0
    for step_state in state.step_states.values():
        total_complexity += complexity_map.get(step_state.tool, 1)  # 默认复杂度为1

    return total_complexity


def _organize_mid_results(state: ExecutionState) -> str:
    mid_results = ''
    for key in sorted(state.step_states.keys()):
        step_state = state.step_states[key]
        if not is_blank(step_state.tool):
            for i in range(len(step_state.tool_states)):
                tool_state = step_state.tool_states[i]
                mid_results += f"## 第{key}步第{i}次调用{step_state.tool}工具\n"
                mid_results += f"### 输入\n{tool_state.input}\n"
                if tool_state.error is None:
                    desc = _load_tool_output_description(state.tool_map[step_state.tool])
                    mid_results += f"### 输出\n{tool_state.result}\n"
                    mid_results += f"### 输出说明\n{desc}\n"
                else:
                    mid_results += f"### 错误信息\n{tool_state.error}\n"
    return mid_results


def _organize_plan(state: ExecutionState) -> str:
    plan = ''
    for key in sorted(state.step_states.keys()):
        step_state = state.step_states[key]
        if is_blank(step_state.tool):
            plan += f"({key}): {step_state.analysis}\n"
        else:
            plan += f"({key}): 调用{step_state.tool}工具，{step_state.analysis}\n"
    return plan


def _load_tool_output_description(tool: BaseTool) -> str:
    """
    从工具描述中提取返回值说明部分

    参数:
        tool (BaseTool): 要分析的工具对象，需包含description属性

    返回:
        str: 工具描述中"Returns:"关键字之后的所有内容，保持原始换行格式
        当未找到"Returns:"关键字时，返回全部文档

    功能说明:
        该函数专门用于解析工具类的描述文档，提取其中的返回值说明部分
        1. 将工具描述按换行分割为多行
        2. 从找到"Returns:"关键字的下一行开始收集内容
        3. 保留原始的换行结构并返回完整的结果字符串
    """
    lines = tool.description.splitlines()
    result = []
    found_keyword = False  # 标志是否已找到关键字
    for line in lines:
        if not found_keyword:
            if "Returns:" in line:
                found_keyword = True  # 找到关键字，下一行开始收集
            continue
        result.append(line)  # 收集关键字之后的每一行
    if len(result) == 0:
        return tool.description
    else:
        return '\n'.join(result)
