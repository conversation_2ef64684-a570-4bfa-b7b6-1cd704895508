#!/usr/bin/env python3
"""
VM性能监控仪表板生成器

## 角色与目标
你是一名阿里云技术支持专家，专门负责生成VM性能监控仪表板。你需要根据真实的VM性能数据，
生成专业、美观的HTML仪表板，用于展示关键性能指标和趋势分析。

## 核心规则
1. **HTML文档输出：** 生成完整的HTML文档，包含所有必要的结构和样式
2. **TAILWIND CSS ONLY：** 必须只使用Tailwind CSS v3的功能类进行样式设计
3. **严禁使用：** 内联样式 (`style="..."`) 或自定义 `<style>` 标签（Chart.js必需的除外）
4. **数据驱动：** 必须基于真实的VM性能数据生成图表和统计信息

## 设计工具箱
在仪表板中使用以下样式突出**关键性能指标**：
- **正常状态指标：** `<span class="text-green-400">正常</span>`
- **警告状态指标：** `<span class="text-yellow-400">警告</span>`
- **异常状态指标：** `<span class="text-red-400">异常</span>`
- **关键数值：** `<span class="bg-blue-500 text-white font-bold py-1 px-2 rounded-md">数值</span>`
- **重要指标名称：** `<span class="text-blue-300 font-semibold">指标名称</span>`
"""

def analyze_vm_metrics(vm_data):
    """
    分析VM性能指标，生成统计摘要
    
    Args:
        vm_data: VM性能数据字典
        
    Returns:
        dict: 包含各指标统计信息的字典
    """
    metrics_summary = {}
    
    for metric_name, data_points in vm_data.items():
        if not data_points:
            continue
            
        values = [point['value'] for point in data_points]
        metrics_summary[metric_name] = {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': sum(values) / len(values),
            'latest': values[-1] if values else 0
        }
    
    return metrics_summary

