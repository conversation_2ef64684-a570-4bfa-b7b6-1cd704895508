# 性能指标配置文件 V2 - 完全动态化配置
# 支持任意指标组合和动态图表生成

# VM性能指标配置 - 扩展版本
vm_metrics:
  title: "附录A. 性能数据分析"
  description: "基于收集的VM性能监控数据，系统自动分析了关键性能指标的趋势和异常情况。以下是详细的性能数据分析报告。"
  
  # 动态图表配置 - 可以任意组合指标
  charts:
    storage_performance:
      title: "存储性能分析"
      metrics:
        - "VmStorageIOLatency/read_iops"
        - "VmStorageIOLatency/write_iops"
      chart_type: "line"
      y_axis_label: "IOPS"
      grid_position: [0, 0]  # 第一行第一列
    
    storage_latency:
      title: "存储延迟分析"
      metrics:
        - "VmStorageIOLatency/read_lat_ms"
        - "VmStorageIOLatency/write_lat_ms"
      chart_type: "line"
      y_axis_label: "延迟 (ms)"
      grid_position: [0, 1]  # 第一行第二列
    
    network_traffic:
      title: "网络流量分析"
      metrics:
        - "VmPpsBps/rx_pps"
        - "VmPpsBps/tx_pps"
      chart_type: "line"
      y_axis_label: "PPS"
      grid_position: [1, 0]  # 第二行第一列
    
    network_quality:
      title: "网络质量分析"
      metrics:
        - "VmVportDropMetric/drop_ratio"
        - "VmNetworkLatency/avg_latency"  # 新增指标
      chart_type: "line"
      y_axis_label: "丢包率 (%)"
      grid_position: [1, 1]  # 第二行第二列
    
    memory_performance:
      title: "内存性能分析"
      metrics:
        - "VmMemBWMetric/memory_bw"
        - "VmMemoryUsage/usage_percent"  # 新增指标
      chart_type: "line"
      y_axis_label: "带宽 (MB/s)"
      grid_position: [2, 0]  # 第三行第一列
    
    cpu_performance:
      title: "CPU性能分析"
      metrics:
        - "VmCpuUsage/cpu_percent"  # 新增指标
        - "VmCpuUsage/load_average"  # 新增指标
      chart_type: "line"
      y_axis_label: "使用率 (%)"
      grid_position: [2, 1]  # 第三行第二列
  
  # 报告配置
  report:
    show_charts: true
    show_recommendations: true
    chart_height: "300px"
    grid_columns: 2  # 图表网格列数
    show_summary_cards: true
    show_trend_analysis: true
  
  # 完整指标定义 - 支持更多指标类型
  metrics:
    # 存储相关指标
    VmStorageIOLatency/read_iops:
      name: "存储读取IOPS"
      unit: ""
      color: "blue"
      category: "storage"
      description: "每秒读取I/O操作数"
      priority: 1
    
    VmStorageIOLatency/write_iops:
      name: "存储写入IOPS"
      unit: ""
      color: "green"
      category: "storage"
      description: "每秒写入I/O操作数"
      priority: 1
    
    VmStorageIOLatency/read_lat_ms:
      name: "读取延迟"
      unit: "ms"
      color: "purple"
      category: "storage"
      description: "存储读取操作延迟"
      priority: 1
    
    VmStorageIOLatency/write_lat_ms:
      name: "写入延迟"
      unit: "ms"
      color: "pink"
      category: "storage"
      description: "存储写入操作延迟"
      priority: 1
    
    # 网络相关指标
    VmPpsBps/rx_pps:
      name: "网络接收PPS"
      unit: ""
      color: "indigo"
      category: "network"
      description: "每秒接收数据包数"
      priority: 1
    
    VmPpsBps/tx_pps:
      name: "网络发送PPS"
      unit: ""
      color: "cyan"
      category: "network"
      description: "每秒发送数据包数"
      priority: 1
    
    VmVportDropMetric/drop_ratio:
      name: "网络丢包率"
      unit: "%"
      color: "red"
      category: "network"
      description: "网络数据包丢失比例"
      priority: 1
    
    VmNetworkLatency/avg_latency:
      name: "网络平均延迟"
      unit: "ms"
      color: "orange"
      category: "network"
      description: "网络通信平均延迟"
      priority: 2
    
    # 内存相关指标
    VmMemBWMetric/memory_bw:
      name: "内存带宽"
      unit: "MB/s"
      color: "orange"
      category: "memory"
      description: "内存带宽使用情况"
      priority: 1
    
    VmMemoryUsage/usage_percent:
      name: "内存使用率"
      unit: "%"
      color: "yellow"
      category: "memory"
      description: "内存使用百分比"
      priority: 1
    
    # CPU相关指标
    VmCpuUsage/cpu_percent:
      name: "CPU使用率"
      unit: "%"
      color: "red"
      category: "cpu"
      description: "CPU使用百分比"
      priority: 1
    
    VmCpuUsage/load_average:
      name: "系统负载"
      unit: ""
      color: "brown"
      category: "cpu"
      description: "系统平均负载"
      priority: 2
    
    # 磁盘相关指标
    VmDiskUsage/usage_percent:
      name: "磁盘使用率"
      unit: "%"
      color: "gray"
      category: "disk"
      description: "磁盘空间使用百分比"
      priority: 2
    
    VmDiskIO/read_bytes:
      name: "磁盘读取速率"
      unit: "MB/s"
      color: "teal"
      category: "disk"
      description: "磁盘读取字节速率"
      priority: 2
    
    VmDiskIO/write_bytes:
      name: "磁盘写入速率"
      unit: "MB/s"
      color: "lime"
      category: "disk"
      description: "磁盘写入字节速率"
      priority: 2
  
  # 阈值配置 - 支持更多指标
  thresholds:
    # 存储阈值
    VmStorageIOLatency/read_lat_ms:
      warning: 10
      critical: 50
      description: "读取延迟超过10ms为警告，超过50ms为严重"
    
    VmStorageIOLatency/write_lat_ms:
      warning: 10
      critical: 50
      description: "写入延迟超过10ms为警告，超过50ms为严重"
    
    VmStorageIOLatency/read_iops:
      warning: 100
      critical: 50
      description: "读取IOPS低于100为警告，低于50为严重"
    
    VmStorageIOLatency/write_iops:
      warning: 100
      critical: 50
      description: "写入IOPS低于100为警告，低于50为严重"
    
    # 网络阈值
    VmVportDropMetric/drop_ratio:
      warning: 0.01
      critical: 0.05
      description: "丢包率超过1%为警告，超过5%为严重"
    
    VmNetworkLatency/avg_latency:
      warning: 50
      critical: 100
      description: "网络延迟超过50ms为警告，超过100ms为严重"
    
    # 内存阈值
    VmMemoryUsage/usage_percent:
      warning: 80
      critical: 95
      description: "内存使用率超过80%为警告，超过95%为严重"
    
    # CPU阈值
    VmCpuUsage/cpu_percent:
      warning: 70
      critical: 90
      description: "CPU使用率超过70%为警告，超过90%为严重"
    
    VmCpuUsage/load_average:
      warning: 2.0
      critical: 4.0
      description: "系统负载超过2.0为警告，超过4.0为严重"
    
    # 磁盘阈值
    VmDiskUsage/usage_percent:
      warning: 80
      critical: 95
      description: "磁盘使用率超过80%为警告，超过95%为严重"

# 数据库性能指标配置 - 扩展版本
database_metrics:
  title: "数据库性能分析"
  description: "基于数据库监控数据的性能分析报告"
  
  charts:
    connection_performance:
      title: "连接性能"
      metrics:
        - "db_connections"
        - "db_active_connections"
      chart_type: "line"
      y_axis_label: "连接数"
    
    query_performance:
      title: "查询性能"
      metrics:
        - "query_response_time"
        - "slow_query_count"
      chart_type: "line"
      y_axis_label: "响应时间 (ms)"
    
    resource_usage:
      title: "资源使用"
      metrics:
        - "cpu_usage"
        - "memory_usage"
      chart_type: "line"
      y_axis_label: "使用率 (%)"
  
  metrics:
    db_connections:
      name: "数据库连接数"
      unit: ""
      color: "blue"
      category: "connection"
      priority: 1
    
    db_active_connections:
      name: "活跃连接数"
      unit: ""
      color: "green"
      category: "connection"
      priority: 1
    
    query_response_time:
      name: "查询响应时间"
      unit: "ms"
      color: "purple"
      category: "performance"
      priority: 1
    
    slow_query_count:
      name: "慢查询数量"
      unit: ""
      color: "red"
      category: "performance"
      priority: 1
    
    cpu_usage:
      name: "CPU使用率"
      unit: "%"
      color: "orange"
      category: "resource"
      priority: 1
    
    memory_usage:
      name: "内存使用率"
      unit: "%"
      color: "yellow"
      category: "resource"
      priority: 1
  
  thresholds:
    query_response_time:
      warning: 100
      critical: 500
    
    cpu_usage:
      warning: 70
      critical: 90
    
    memory_usage:
      warning: 80
      critical: 95
    
    slow_query_count:
      warning: 10
      critical: 50
  
  report:
    show_charts: true
    show_recommendations: true
    chart_height: "300px"
    grid_columns: 2

# 网络设备性能指标配置 - 扩展版本
network_metrics:
  title: "网络设备性能分析"
  description: "基于网络设备监控数据的性能分析报告"
  
  charts:
    bandwidth_analysis:
      title: "带宽分析"
      metrics:
        - "bandwidth_utilization"
        - "throughput"
      chart_type: "line"
      y_axis_label: "带宽利用率 (%)"
    
    quality_analysis:
      title: "网络质量"
      metrics:
        - "packet_loss"
        - "latency"
        - "jitter"
      chart_type: "line"
      y_axis_label: "丢包率 (%)"
  
  metrics:
    bandwidth_utilization:
      name: "带宽利用率"
      unit: "%"
      color: "blue"
      category: "bandwidth"
      priority: 1
    
    throughput:
      name: "吞吐量"
      unit: "Mbps"
      color: "green"
      category: "bandwidth"
      priority: 1
    
    packet_loss:
      name: "丢包率"
      unit: "%"
      color: "red"
      category: "quality"
      priority: 1
    
    latency:
      name: "网络延迟"
      unit: "ms"
      color: "purple"
      category: "quality"
      priority: 1
    
    jitter:
      name: "抖动"
      unit: "ms"
      color: "orange"
      category: "quality"
      priority: 2
  
  thresholds:
    bandwidth_utilization:
      warning: 70
      critical: 90
    
    packet_loss:
      warning: 0.1
      critical: 1.0
    
    latency:
      warning: 50
      critical: 100
    
    jitter:
      warning: 10
      critical: 20
  
  report:
    show_charts: true
    show_recommendations: true
    chart_height: "300px"
    grid_columns: 2