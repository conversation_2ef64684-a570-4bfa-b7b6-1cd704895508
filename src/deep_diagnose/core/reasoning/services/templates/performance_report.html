<!-- 性能报告HTML模板 V4.2 - 修复图表JS渲染逻辑 -->
<div class="bg-blue-900/50 border-l-4 border-blue-400 p-6 rounded-lg mb-8">
    <h2 class="text-2xl font-bold text-blue-400 pb-2 mb-4">{{ title }}</h2>
    <p class="text-xl text-gray-100 leading-relaxed mb-6">{{ description }}</p>

    {% for instance_id, instance_analysis in analysis_data.items() %}
    <div class="instance-section mb-12 bg-gray-800/30 rounded-lg p-4">
        <h3 class="text-2xl font-bold text-teal-300 mb-4 border-b border-teal-500 pb-2">实例: {{ instance_id }}</h3>

        {% set metrics_cards = {} %}
        {% if instance_analysis %}
            {% for metric_key, stats in instance_analysis.items() %}
                {% if metric_key in metrics_config %}
                    {% set metric_config = metrics_config[metric_key] %}
                    {% set _ = metrics_cards.update({metric_key: {
                        'name': metric_config.get('name', metric_key),
                        'unit': metric_config.get('unit', ''),
                        'latest': stats.latest,
                        'avg': stats.avg
                    }}) %}
                {% endif %}
            {% endfor %}
        {% endif %}

        <!-- 指标卡片 -->
        {% if report_config.get('show_summary_cards') and metrics_cards %}
        <div class="bg-gray-800/50 rounded-lg p-4 mb-6">
            <h4 class="text-lg font-bold text-blue-300 mb-3">性能指标总览</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for metric_key, metric_data in metrics_cards.items() %}
                <div class="bg-gray-700/50 rounded-lg p-4">
                    <h5 class="text-sm font-medium text-gray-300">{{ metric_data.name }}</h5>
                    <div class="text-2xl font-bold text-white">{{ "%.2f"|format(metric_data.latest) }}{{ metric_data.unit }}</div>
                    <div class="text-sm text-gray-400">平均: {{ "%.2f"|format(metric_data.avg) }}{{ metric_data.unit }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 图表 -->
        {% if report_config.get('show_charts') %}
        <div class="bg-gray-800/50 rounded-lg p-4 mb-6">
            <h4 class="text-lg font-bold text-blue-300 mb-4">性能趋势图表</h4>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {% for chart_id, chart_config in charts_config.items() %}
                <div class="bg-gray-700/50 rounded-lg p-4">
                    <h5 class="text-md font-semibold text-blue-200">{{ chart_config.title }}</h5>
                    <div class="chart-container" style="height: {{ report_config.get('chart_height', '300px') }};">
                        <canvas id="{{ chart_id }}_{{ instance_id }}"></canvas>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

    </div>
    {% endfor %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const rawData = {{ raw_data|tojson }};
    const metricsConfig = {{ metrics_config|tojson }};
    const chartsConfig = {{ charts_config|tojson }};

    function parseDateTime(dateTimeStr) {
        return new Date(dateTimeStr.replace(' ', 'T'));
    }

    function getColor(colorName, alpha = 1) {
        const colors = {
            blue: `rgba(59, 130, 246, ${alpha})`,
            green: `rgba(34, 197, 94, ${alpha})`,
            purple: `rgba(147, 51, 234, ${alpha})`,
            pink: `rgba(236, 72, 153, ${alpha})`,
            indigo: `rgba(99, 102, 241, ${alpha})`,
            cyan: `rgba(6, 182, 212, ${alpha})`,
            orange: `rgba(249, 115, 22, ${alpha})`,
            red: `rgba(239, 68, 68, ${alpha})`,
            yellow: `rgba(234, 179, 8, ${alpha})`
        };
        return colors[colorName] || colors.blue;
    }

    function createChart(canvasId, datasets, yLabel, chartType) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;
        new Chart(ctx.getContext('2d'), {
            type: chartType || 'line',
            data: { datasets },
            options: {
                responsive: true, maintainAspectRatio: false, interaction: { intersect: false, mode: 'index' },
                scales: {
                    x: {
                        type: 'linear', // 使用线性轴
                        position: 'bottom',
                        title: { display: true, text: '时间' },
                        ticks: {
                            // 将数字时间戳格式化为时间字符串
                            callback: function(value, index, values) {
                                return new Date(value).toLocaleTimeString('zh-CN', {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                });
                            }
                        }
                    },
                    y: { beginAtZero: true, title: { display: true, text: yLabel } }
                }
            }
        });
    }

    for (const [instance_id, instance_data] of Object.entries(rawData)) {
        for (const [chart_id, chart_config] of Object.entries(chartsConfig)) {
            const canvasId = `${chart_id}_${instance_id}`;
            const datasets = [];

            chart_config.metrics.forEach(metricKey => {
                if (instance_data[metricKey] && Array.isArray(instance_data[metricKey])) {
                    const metricInfo = metricsConfig[metricKey] || {};
                    const color = metricInfo.color || 'blue';
                    datasets.push({
                        label: metricInfo.name || metricKey,
                        // 将日期转换为数字时间戳
                        data: instance_data[metricKey].map(d => ({ x: parseDateTime(d.datetime).getTime(), y: d.value })),
                        borderColor: getColor(color),
                        backgroundColor: getColor(color, 0.1),
                        fill: true
                    });
                }
            });

            if (datasets.length > 0) {
                createChart(canvasId, datasets, chart_config.y_axis_label || 'Value', chart_config.chart_type);
            } else {
                const canvas = document.getElementById(canvasId);
                if(canvas) canvas.parentElement.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">No data for this chart</div>`;
            }
        }
    }
});
</script>