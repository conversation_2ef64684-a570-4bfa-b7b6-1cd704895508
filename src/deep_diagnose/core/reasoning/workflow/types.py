import operator
from typing import Annotated, Optional, List, Union, Literal
from dataclasses import dataclass, field

from langgraph.graph import MessagesState

from deep_diagnose.prompts.planner_model import Plan


@dataclass
class ReportState:
    """单次诊断报告状态（版本化）"""
    report_html: str = ""
    iteration: int = 1
    generator: str = ""
    validate_status: Literal["pending", "passed", "failed"] = "pending"
    reflection_status: Literal["pending", "passed", "failed"] = "pending"
    validate_error: Union[dict, str, None] = None
    reflection_problems: List[str] = field(default_factory=list)
    generation_feedback: dict = field(default_factory=dict) # 用于向生成器提供反馈
    url: Optional[str] = None
    path: Optional[str] = None


class ReasoningState(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "zh-CN"
    request_id: str = ""  # Primary identifier - replaces thread_id usage
    observations: list[str] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    mcp_servers_description: str = ""
    sop_id: str = ""
    operation_sop_content: str = ""

    # HTML 报告版本列表（按时间顺序追加）
    reports: List[ReportState] = field(default_factory=list)
    
    # 并发HTML片段（新增字段）
    problem_description_html: str = ""
    diagnosis_info_html: str = ""
    key_findings_html: str = ""
    evidence_chain_html: str = ""
    summary_conclusion_html: str = ""
    merged_html_report: str = ""


# Backward compatibility: keep State as an alias for ReasoningState
State = ReasoningState