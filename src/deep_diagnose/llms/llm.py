from langchain_openai import Chat<PERSON>penAI
from langchain_core.runnables.fallbacks import RunnableWithFallbacks

from deep_diagnose.common.config import get_config
from deep_diagnose.common.config.constants.agents import LLMType
from deep_diagnose.common.config.core.base_config import DotDict
from deep_diagnose.llms.chat_qwen import ChatQwen

# Cache for LLM instances
_llm_cache: dict[LLMType, ChatOpenAI] = {}
# Cache for LLM instances with fallbacks
_llm_fallback_cache: dict[LLMType, RunnableWithFallbacks] = {}


def _create_llm_use_conf(llm_type: LLMType, conf: DotDict) -> ChatOpenAI:
    llm_type_map = {
        "reasoning": conf.llm.profiles.reasoning,
        "basic": conf.llm.profiles.basic,
        "vision": conf.llm.profiles.vision,
        "code_flash": conf.llm.profiles.code_flash,
        "glm": conf.llm.profiles.glm,
        "code": getattr(conf.llm.profiles, 'code', conf.llm.profiles.reasoning),
    }
    llm_conf = llm_type_map.get(llm_type)
    if not llm_conf:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    if not isinstance(llm_conf, dict):
        raise ValueError(f"Invalid LLM Conf: {llm_type}")

    # 过滤掉fallback_models字段，避免传递给ChatQwen构造函数
    filtered_conf = {k: v for k, v in llm_conf.items() if k != 'fallback_models'}
    return ChatQwen(**filtered_conf)


def get_llm_by_type(
    llm_type: LLMType,
) -> ChatOpenAI:
    """
    Get LLM instance by type. Returns cached instance if available.
    """
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]
    conf = get_config()
    llm = _create_llm_use_conf(llm_type, conf)
    _llm_cache[llm_type] = llm
    return llm


def _get_fallback_models(llm_type: LLMType, conf: DotDict) -> list[str]:
    """
    从配置文件获取fallback模型名称列表

    Args:
        llm_type: 主要LLM类型
        conf: 配置对象

    Returns:
        fallback模型名称列表，按优先级排序
    """
    # 尝试从配置文件读取fallback_models
    try:
        llm_type_map = {
            "reasoning": conf.llm.profiles.reasoning,
            "basic": conf.llm.profiles.basic,
            "vision": conf.llm.profiles.vision,
            "code_flash": conf.llm.profiles.code_flash,
            "glm": conf.llm.profiles.glm,
            "code": getattr(conf.llm.profiles, 'code', conf.llm.profiles.reasoning),
        }

        llm_conf = llm_type_map.get(llm_type)
        if llm_conf and isinstance(llm_conf, dict) and 'fallback_models' in llm_conf:
            return llm_conf['fallback_models']
    except (AttributeError, KeyError):
        pass

    # 如果配置文件中没有，使用默认fallback策略（模型名称）
    default_fallback_strategies = {
        "reasoning": ["qwen-plus-latest"],  # reasoning模型fallback到qwen-plus-latest
        "basic": [],  # basic模型没有fallback
        "vision": ["qwen-plus-latest"],  # vision模型fallback到qwen-plus-latest
        "code": ["qwen3-coder-flash", "qwen-plus-latest"],  # code模型fallback到qwen3-coder-flash，再到qwen-plus-latest
        "code_flash": ["qwen-plus-latest"],  # code_flash模型fallback到qwen-plus-latest
        "glm": ["qwen-plus-latest"],  # glm模型fallback到qwen-plus-latest
    }

    return default_fallback_strategies.get(llm_type, ["qwen-plus-latest"])


def _create_llm_from_model_name(model_name: str, conf: DotDict) -> ChatOpenAI:
    """
    根据模型名称创建LLM实例

    Args:
        model_name: 模型名称
        conf: 配置对象

    Returns:
        LLM实例
    """
    # 使用基础配置创建LLM
    base_config = {
        "base_url": conf.llm.tongyi_provider.base_url,
        "api_key": conf.llm.tongyi_provider.api_key,
        "model": model_name
    }
    return ChatQwen(**base_config)


def get_llm_with_fallback(
    llm_type: LLMType,
    fallback_enable: bool = True
) -> ChatOpenAI | RunnableWithFallbacks:
    """
    获取带有fallback机制的LLM实例
    使用LangChain原生的with_fallbacks能力

    Args:
        llm_type: LLM类型
        fallback_enable: 是否启用fallback机制

    Returns:
        LLM实例，如果启用fallback且有fallback模型，返回RunnableWithFallbacks
    """
    if not fallback_enable:
        return get_llm_by_type(llm_type)

    conf = get_config()

    # 检查缓存
    cache_key = llm_type
    if cache_key in _llm_fallback_cache:
        return _llm_fallback_cache[cache_key]

    # 获取主要LLM
    primary_llm = get_llm_by_type(llm_type)

    # 获取fallback模型名称列表
    fallback_model_names = _get_fallback_models(llm_type, conf)

    # 如果没有fallback模型，直接返回主要LLM
    if not fallback_model_names:
        _llm_fallback_cache[cache_key] = primary_llm
        return primary_llm

    # 创建fallback模型列表
    fallback_models = []
    for model_name in fallback_model_names:
        try:
            fallback_llm = _create_llm_from_model_name(model_name, conf)
            fallback_models.append(fallback_llm)
        except Exception as e:
            # 如果fallback模型创建失败，记录但继续
            print(f"Warning: Failed to create fallback model {model_name}: {e}")

    # 如果有fallback模型，使用with_fallbacks
    if fallback_models:
        llm_with_fallback = primary_llm.with_fallbacks(fallback_models)
        _llm_fallback_cache[cache_key] = llm_with_fallback
        return llm_with_fallback
    else:
        _llm_fallback_cache[cache_key] = primary_llm
        return primary_llm


