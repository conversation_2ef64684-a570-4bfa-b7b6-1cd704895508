"""
Report API V1 路由

提供报告访问接口，通过request_id从数据库查询AI消息获取URLs。
"""

import logging
import json
import asyncio
from typing import Optional

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import HTMLResponse

from deep_diagnose.api.models.user import UserModel
from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.domain.chat.models import MessageType

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["Report V1"])


@router.get(
    "/reporter/{request_id}",
    summary="获取诊断报告",
    description="""
    通过request_id获取诊断报告内容。

    **路径参数：**
    - `request_id`: 必需，请求ID

    **功能说明：**
    1. 根据request_id直接从OSS获取报告文件
    2. 文件路径：diagnosis-reports/{request_id}.html
    3. 直接返回HTML内容给客户端

    **响应格式：**
    - 成功：返回HTML格式的诊断报告
    - 失败：返回JSON格式的错误信息
    """,
    responses={
        200: {
            "description": "成功返回HTML报告内容",
            "content": {
                "text/html": {
                    "example": "<!DOCTYPE html><html>...</html>"
                }
            },
        },
        404: {"description": "报告不存在"},
        500: {"description": "服务器内部错误"},
    },
)
async def get_report(
        request_id: str,
        user: UserModel = Depends(get_current_user)
):
    """
    获取诊断报告

    Args:
        request_id: 请求ID
        user: 当前用户

    Returns:
        HTMLResponse: HTML格式的诊断报告内容
    """
    logger.info(f"获取报告请求 - request_id: {request_id}, 用户: {getattr(user, 'user_id', 'anonymous')}")

    try:
        # 直接从OSS获取报告内容
        html_content = await _fetch_report_from_oss(request_id)
        if not html_content:
            logger.warning(f"request_id {request_id} 的报告文件未找到")
            # 返回友好的等待提示信息
            html_content = _get_wait_page_html()

        logger.info(f"{'成功获取' if html_content else '报告未找到，返回等待页面'} request_id {request_id} 的报告内容")

        # 返回HTML内容
        return HTMLResponse(
            content=html_content,
            status_code=200,
            headers={
                "Content-Type": "text/html; charset=utf-8",
                "Cache-Control": "no-cache",  # 不缓存等待页面
            }
        )

    except Exception as e:
        logger.error(f"获取报告失败 - request_id: {request_id}, 错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取报告失败: {str(e)}"
        )


def _get_wait_page_html() -> str:
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>诊断报告生成中</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                background-color: #f5f5f5;
                margin: 0;
                padding: 0;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }}
            .container {{
                text-align: center;
                background: white;
                padding: 40px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                max-width: 500px;
            }}
            .spinner {{
                border: 4px solid #f3f3f3;
                border-top: 4px solid #3498db;
                border-radius: 50%;
                width: 40px;
                height: 40px;
                animation: spin 2s linear infinite;
                margin: 0 auto 20px auto;
            }}
            @keyframes spin {{
                0% {{ transform: rotate(0deg); }}
                100% {{ transform: rotate(360deg); }}
            }}
            h1 {{
                color: #333;
                margin-bottom: 15px;
            }}
            p {{
                color: #666;
                line-height: 1.6;
            }}
            .note {{
                margin-top: 20px;
                padding: 15px;
                background-color: #e8f4ff;
                border-radius: 4px;
                color: #555;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="spinner"></div>
            <h1>诊断报告正在生成中</h1>
            <p>系统正在为您生成诊断报告，请稍等片刻...</p>
            <div class="note">
                <p>提示：您可以通过刷新页面来检查报告是否已生成。</p>
                <p>如果长时间未生成，请联系技术支持。</p>
            </div>
        </div>
    </body>
    </html>
    """
    return html_content


async def _get_report_url_from_message(request_id: str) -> Optional[str]:
    """
    从数据库message表中获取报告URL

    Args:
        request_id: 请求ID

    Returns:
        Optional[str]: 报告URL或None
    """
    try:
        # 查询AI消息
        from deep_diagnose.domain.chat.models import CloudbotAgentChatMessage

        ai_message = await CloudbotAgentChatMessage.filter(
            request_id=request_id,
            message_type=MessageType.AI_RESPONSE.value
        ).first()

        if not ai_message:
            logger.debug(f"未找到request_id {request_id} 的AI消息")
            return None

        # 解析消息内容为JSON
        try:
            message_data = json.loads(ai_message.message)
        except json.JSONDecodeError as e:
            logger.warning(f"AI消息内容不是有效的JSON: {e}")
            return None

        # 提取urls字段
        urls = message_data.get('urls', [])
        if not urls or not isinstance(urls, list):
            logger.debug(f"AI消息中未找到urls字段或urls为空")
            return None

        # 获取第一个URL
        first_url_info = urls[0]
        if isinstance(first_url_info, dict):
            report_url = first_url_info.get('url')
        elif isinstance(first_url_info, str):
            report_url = first_url_info
        else:
            logger.warning(f"无法解析URL信息: {first_url_info}")
            return None

        if report_url:
            logger.debug(f"从AI消息获取到报告URL: {report_url}")
            return report_url
        else:
            logger.debug(f"AI消息中的URL字段为空")
            return None

    except Exception as e:
        logger.error(f"从数据库获取报告URL失败: {e}", exc_info=True)
        return None


async def _fetch_report_from_oss(request_id: str) -> Optional[str]:
    """
    直接从OSS获取报告内容

    Args:
        request_id: 请求ID

    Returns:
        Optional[str]: HTML内容或None
    """
    try:
        from deep_diagnose.storage.oss_client import OssClient
        import tempfile
        import os

        oss_client = OssClient()
        oss_key = f"diagnosis-reports/{request_id}.html"

        # 下载到临时文件
        with tempfile.NamedTemporaryFile(mode="w+", suffix=".html", delete=False, encoding="utf-8") as temp_file:
            temp_path = temp_file.name

        try:
            # 从OSS下载文件
            await asyncio.get_event_loop().run_in_executor(
                None, oss_client.download_file, oss_key, temp_path
            )

            # 读取文件内容
            with open(temp_path, 'r', encoding='utf-8') as f:
                content = f.read()

            logger.debug(f"成功从OSS获取报告，大小: {len(content)} 字符")
            return content

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except Exception as e:
        logger.error(f"从OSS获取报告失败: {e}, request_id: {request_id}", exc_info=True)
        return None


@router.get(
    "/report/{request_id}/url",
    summary="获取报告URL",
    description="""
    通过request_id获取报告URL。

    **路径参数：**
    - `request_id`: 必需，请求ID

    **响应格式：**
    ```json
    {
        "request_id": "请求ID",
        "url": "报告URL",
        "source": "database"
    }
    ```
    """,
    responses={
        200: {"description": "成功返回报告URL"},
        404: {"description": "报告URL不存在"},
        500: {"description": "服务器内部错误"},
    },
)
async def get_report_url(
        request_id: str,
        user: UserModel = Depends(get_current_user)
):
    """
    获取报告URL

    Args:
        request_id: 请求ID
        user: 当前用户

    Returns:
        dict: 包含报告URL的响应
    """
    logger.info(f"获取报告URL请求 - request_id: {request_id}, 用户: {getattr(user, 'user_id', 'anonymous')}")

    try:
        # 从数据库获取报告URL
        report_url = await _get_report_url_from_message(request_id)
        if not report_url:
            logger.warning(f"request_id {request_id} 的报告URL未找到")
            raise HTTPException(
                status_code=404,
                detail=f"request_id {request_id} 的报告URL不存在"
            )

        logger.info(f"成功获取 request_id {request_id} 的报告URL")

        return {
            "request_id": request_id,
            "url": report_url,
            "source": "database"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取报告URL失败 - request_id: {request_id}, 错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"获取报告URL失败: {str(e)}"
        )
