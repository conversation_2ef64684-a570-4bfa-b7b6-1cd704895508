"""

测试 Chat V1 API 返回的 SSE 事件，确保与 test_task_api_integration.py 中的 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import json
from typing import Dict, Any, List, Optional
import httpx
import pytest
import os
import time
from datetime import datetime

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ChatV1APITester:
    """Chat V1 API 测试器 - 专注于 SSE 事件兼容性"""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"
    
    def _format_event_for_display(self, event: Dict[str, Any]) -> str:
        """将事件中的 data 字段（若为 JSON 字符串）进行 pretty JSON 展示，以提升可读性。
        保持其他字段不变。
        """
        try:
            # 浅拷贝，避免修改原对象
            display_event: Dict[str, Any] = dict(event)
            data_val = display_event.get("data")
            if isinstance(data_val, str):
                # 仅当明显是 JSON 时尝试解析
                trimmed = data_val.strip()
                if (trimmed.startswith("{") and trimmed.endswith("}")) or (
                    trimmed.startswith("[") and trimmed.endswith("]")
                ):
                    try:
                        display_event["data"] = json.loads(trimmed)
                    except Exception:
                        # 保持原样
                        pass
            return json.dumps(display_event, ensure_ascii=False, indent=2)
        except Exception:
            # 兜底：直接返回原始事件的字符串化
            return json.dumps(event, ensure_ascii=False, indent=2)
    
    async def authenticate(self) -> str:
        """获取认证token - 与 V1 API 相同的认证方式"""
        auth_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/token",
                json=auth_data,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get("access_token")
            else:
                raise Exception(f"Authentication failed: {response.status_code} - {response.text}")
    
    async def _stream_chat(self, request_data: Dict[str, Any], *, save_to_file: bool = True, file_prefix: str = "sse_events_integration") -> Dict[str, Any]:
        """通用的 SSE 事件流收集器，减少重复逻辑，并输出耗时统计信息。"""
        sse_events: List[str] = []
        parsed_events: List[Dict[str, Any]] = []
        first_token_seconds: Optional[float] = None
        stream_duration_seconds: Optional[float] = None
        content_type: Optional[str] = None

        # 创建输出目录（如果不存在）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "tests/integration/api/sse_outputs"
        os.makedirs(output_dir, exist_ok=True)
        sse_file_path = f"{output_dir}/{file_prefix}_{timestamp}.txt"

        start_wall = datetime.now()
        start_perf = time.perf_counter()

        if save_to_file:
            print(f"\n🚀 开始调用 Chat V1 API...")
            q = request_data.get("question", "")
            print(f"📝 问题: {q[:100]}...")
            print(f"🕒 开始时间: {start_wall.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📁 SSE 事件将保存到: {sse_file_path}")

        async with httpx.AsyncClient(timeout=httpx.Timeout(900.0)) as client:
            async with client.stream(
                "POST",
                f"{self.base_url}/api/v1/chat",
                json=request_data,
                headers=self.headers,
            ) as response:
                if response.status_code != 200:
                    raise Exception(
                        f"Chat V1 request failed: {response.status_code} - {await response.aread()}"
                    )

                content_type = response.headers.get("content-type", "")
                print(f"✅ 连接成功，Content-Type: {content_type}")
                print("=" * 80)

                last_event_perf = None
                async for line in response.aiter_lines():
                    if not line.strip():
                        continue
                    sse_events.append(line)

                    if line.startswith("data: "):
                        data_content = line[6:]
                        if data_content == "[DONE]":
                            # 当服务端发出结束标记，我们以最后一个事件时间作为结束
                            last_event_perf = last_event_perf or time.perf_counter()
                            break

                        try:
                            event_data = json.loads(data_content)
                            parsed_events.append(event_data)

                            # 记录 first token 延迟
                            if first_token_seconds is None:
                                first_token_seconds = time.perf_counter() - start_perf

                            # 更新最近事件时间
                            last_event_perf = time.perf_counter()

                            print(f"🚀 解析 SSE 事件 [{len(parsed_events):03d}]:\n" + self._format_event_for_display(event_data))
                        except json.JSONDecodeError as e:
                            print(f"Failed to parse event: {data_content}, error: {e}")
                            continue

                # 流结束后统计总耗时（从请求开始到最后一个事件）
                if last_event_perf is None:
                    # 没有收到任何 data 事件的情况
                    stream_duration_seconds = time.perf_counter() - start_perf
                else:
                    stream_duration_seconds = last_event_perf - start_perf

        # 打印统计信息
        if save_to_file:
            print(f"\n📊 SSE 事件接收完成！")
            print(f"📈 总事件数(行): {len(sse_events)}")
            print(f"📈 解析事件数: {len(parsed_events)}")
            if first_token_seconds is not None:
                print(f"⏱️ First token 耗时: {first_token_seconds:.3f}s")
            print(f"⏱️ 所有 SSE 事件耗时: {stream_duration_seconds:.3f}s")

        # 保存事件到文件
        if save_to_file and sse_events:
            try:
                self._write_sse_report(
                    sse_file_path,
                    request_data.get("question", ""),
                    sse_events,
                    parsed_events,
                    {
                        "content_type": content_type,
                        "first_token_seconds": first_token_seconds,
                        "stream_duration_seconds": stream_duration_seconds,
                        "sse_lines": len(sse_events),
                        "parsed_count": len(parsed_events),
                        "start_time": start_wall.strftime('%Y-%m-%d %H:%M:%S'),
                    },
                )
                print(f"✅ SSE 事件已保存到文件: {sse_file_path}")
            except Exception as e:
                print(f"⚠️  保存文件失败: {e}")

        return {
            "sse_events": sse_events,
            "parsed_events": parsed_events,
            "total_events": len(parsed_events),
            "file_path": sse_file_path if save_to_file else None,
            "metrics": {
                "first_token_seconds": first_token_seconds,
                "stream_duration_seconds": stream_duration_seconds,
                "content_type": content_type,
                "sse_lines": len(sse_events),
                "parsed_count": len(parsed_events),
            },
        }

    def _write_sse_report(
        self,
        file_path: str,
        question: str,
        raw_lines: List[str],
        parsed_events: List[Dict[str, Any]],
        metrics: Dict[str, Any],
    ) -> None:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(f"# Chat V1 API SSE 事件记录 - Integration Test\n")
            f.write(f"# 时间: {metrics.get('start_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))}\n")
            f.write(f"# 问题: {question}\n")
            f.write(f"# Content-Type: {metrics.get('content_type', '')}\n")
            f.write(f"# SSE 总行数: {metrics.get('sse_lines', len(raw_lines))}\n")
            f.write(f"# 解析事件数: {metrics.get('parsed_count', len(parsed_events))}\n")
            if metrics.get("first_token_seconds") is not None:
                f.write(f"# First token 耗时: {metrics['first_token_seconds']:.3f}s\n")
            f.write(f"# 所有 SSE 事件耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s\n")
            f.write("# " + "=" * 70 + "\n\n")

            # 写入原始 SSE 事件
            f.write("# 原始 SSE 事件\n")
            f.write("# " + "=" * 70 + "\n\n")
            for i, event in enumerate(raw_lines, 1):
                f.write(f"[事件 {i:03d}] {event}\n")

            # 写入解析后的事件数据
            f.write("\n# " + "=" * 70 + "\n")
            f.write("# 解析后的事件数据 (JSON 格式)\n")
            f.write("# " + "=" * 70 + "\n\n")
            for i, event in enumerate(parsed_events, 1):
                f.write(f"# 解析事件 {i:03d}\n")
                f.write(self._format_event_for_display(event))
                f.write("\n\n")

            # 写入事件类型统计
            event_types: Dict[str, int] = {}
            for event in parsed_events:
                et = self._extract_event_type(event)
                event_types[et] = event_types.get(et, 0) + 1

            f.write("# " + "=" * 70 + "\n")
            f.write("# 事件类型统计\n")
            f.write("# " + "=" * 70 + "\n\n")
            for event_type, count in event_types.items():
                f.write(f"# {event_type}: {count} 次\n")

    def _extract_event_type(self, event: Dict[str, Any]) -> str:
        """兼容从顶层或嵌套 data 中获取 event_type。"""
        if "event_type" in event and isinstance(event["event_type"], str):
            return event["event_type"]
        # 尝试从 data 中获取
        data_val = event.get("data")
        try:
            if isinstance(data_val, str):
                trimmed = data_val.strip()
                if (trimmed.startswith("{") and trimmed.endswith("}")) or (
                    trimmed.startswith("[") and trimmed.endswith("]")
                ):
                    data_val = json.loads(trimmed)
        except Exception:
            pass
        if isinstance(data_val, dict) and isinstance(data_val.get("event_type"), str):
            return data_val["event_type"]
        return "unknown"

    async def test_chat_v1_sse_events(self, question: str, save_to_file: bool = True) -> Dict[str, Any]:
        """
        测试 Chat V1 API 的 SSE 事件（使用通用流式方法），并统计时延信息。
        """
        request_data = {
            "question": question,
            "agent": "ReasoningAgent",
            "user_id": "149789",
        }
        return await self._stream_chat(request_data, save_to_file=save_to_file, file_prefix="chat_v1")

    async def test_interactive_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InteractiveAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InteractiveAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="interactive_agent")


    async def test_inspect_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InspectAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InspectAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="inspect_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_chat_v1_api_sse_events_e2e():
    """
    Chat V1 API SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 Chat V1 API SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据

    #test_question="你有哪些能力呢"
    test_question = "利用vmcore分析这个NC *********** 在20250826的宕机原因"
    ##test_question ="查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 Chat V1 API
    print("🚀 开始测试 Chat V1 API...")
    try:
        result = await tester.test_chat_v1_sse_events(test_question, save_to_file=True)
        
        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        if result.get("file_path"):
            print(f"✅ 事件文件: {result['file_path']}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 Chat V1 API SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")

@pytest.mark.asyncio
@pytest.mark.integration  
async def skip_test_chat_v1_api_sse_events_e2e_interactive_agent():
    """
    测试 InteractiveAgent 的 SSE 事件端到端测试（需要服务器运行）
    """
    print("\n" + "="*80)
    print("开始 InteractiveAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    
    print(f"📝 测试问题: {test_question}")
    
    # 测试 InteractiveAgent
    print("🚀 开始测试 InteractiveAgent SSE 事件...")
    try:
        result = await tester.test_interactive_agent_sse_events(test_question, additional_info={"context": "系统日志分析"})
        
        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 InteractiveAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_chat_v1_api_sse_events_e2e_inspect_agent():
    """
    测试 InspectAgent 的 SSE 事件端到端测试（需要服务器运行）
    """
    print("\n" + "="*80)
    print("开始 InspectAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ChatV1APITester()
    
    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    
    print(f"📝 测试问题: {test_question}")

    # 测试 InspectAgent
    print("🚀 开始测试 InspectAgent SSE 事件...")
    test_cases = [
        {
            "machine_id": "i-bp1fz27ong6p6w693vn5",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
        {
            "machine_id": "***********",
            "description": "NC IP Address 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
        {
            "machine_id": "i-2zebte5jxlkv2jscbwjb",
            "description": "ECS Instance ID 测试",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
    ]

    try:
        result = await tester.test_inspect_agent_sse_events(test_question, additional_info=test_cases[0])

        # 验证基本结果
        assert result["total_events"] > 0, "应该收到事件"
        
        print(f"✅ 收到 {result['total_events']} 个事件")
        metrics = result.get("metrics", {})
        if metrics:
            if metrics.get("first_token_seconds") is not None:
                print(f"⏱️ First token: {metrics['first_token_seconds']:.3f}s")
            print(f"⏱️ SSE 总耗时: {metrics.get('stream_duration_seconds', 0.0):.3f}s")
            print(f"📦 SSE 总行数: {metrics.get('sse_lines', 0)}")
        
        # 验证 SSE 格式
        event_lines = [line for line in result["sse_events"] if line.startswith("event: ")]
        data_lines = [line for line in result["sse_events"] if line.startswith("data: ")]
        
        print(f"✅ event: 行数: {len(event_lines)}")
        print(f"✅ data: 行数: {len(data_lines)}")
        
        assert len(data_lines) > 0, "应该包含 data: 行"
        
        print("🎉 InteractiveAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行测试
    
    运行方式：
    # 无需服务器的测试
    python tests/integration/api/test_chat_v1_integration.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_chat_v1_integration.py -v -s

    pytest tests/integration/api/test_chat_v1_integration.py::test_chat_v1_api_sse_events_e2e_interactive_agent -v -s
    """

    # pytest.main([__file__, "-v", "-s", "--tb=short"])
    # 如果需要测试 InteractiveAgent，可以取消注释以下行
    pytest.main([__file__, "-v", "-s", "--tb=short", "--runxfail", "test_chat_v1_api_sse_events_e2e_interactive_agent"])
    print("测试完成！")
    