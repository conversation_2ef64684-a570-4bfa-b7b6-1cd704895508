import unittest
from unittest.mock import Mock

from deep_diagnose.core.interactive.param_manager import ContextualParamManager


class ParamManagerTest(unittest.TestCase):

    def setUp(self):
        self.manager = ContextualParamManager()

    def test_validate_valid_params(self):
        context = ("实例ID i-wz9b873bysppltjo2qpe 和时间范围 2025-08-27 10:05:17 至 2025-09-03 10:05:17"
                   "查询该实例近一周的冷迁移记录，获取迁移状态、迁移时间、源NC、目的NC和迁移原因等信息无")
        param = {
            "instanceId": "i-wz9b873bysppltjo2qpe",
            "startTime": "2025-08-27 10:05:17",
            "endTime": "2025-09-03 10:05:17"
        }
        result = self.manager.validate_params(param, context)
        self.assertIsNone(result)

    def test_validate_invalid_param_not_in_context(self):
        context = "一些文本内容"
        param = {
            "instanceId": "i-wz9b873bysppltjo2qpe"
        }
        result = self.manager.validate_params(param, context)
        self.assertEqual(result, "i-wz9b873bysppltjo2qpe is not found in context")

    def test_validate_invalid_param_pattern_mismatch(self):
        context = "实例ID i-wz9b873bysppltjo2qpe"
        param = {
            "ncIp": "i-wz9b873bysppltjo2qpe"
        }
        result = self.manager.validate_params(param, context)
        self.assertEqual(result, "i-wz9b873bysppltjo2qpe does not match regex pattern")

    def test_validate_not_dict_param(self):
        context = "some context"
        param = "not a dict"
        result = self.manager.validate_params(param, context)
        self.assertEqual(result, "Generated param is not dict")

    def test_extract_valid_params(self):
        mock_tool = Mock()
        mock_tool.args = {
            "instanceId": {"type": "string"},
            "startTime": {"type": "string"},
            "endTime": {"type": "string"}
        }

        context = ("实例ID i-wz9b873bysppltjo2qpe 和时间范围 2025-08-27 10:05:17 至 2025-09-03 10:05:17"
                   "查询该实例近一周的冷迁移记录，获取迁移状态、迁移时间、源NC、目的NC和迁移原因等信息无")
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "instanceId": "i-wz9b873bysppltjo2qpe",
            "startTime": "2025-08-27 10:05:17",
            "endTime": "2025-09-03 10:05:17"
        }
        self.assertEqual(result, expected)

    def test_extract_event_id(self):
        mock_tool = Mock()
        mock_tool.args = {
            "eventId": {"type": "string"},
        }

        context = "事件ID为 e-abc123def，请处理"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "eventId": "e-abc123def"
        }
        self.assertEqual(result, expected)

    def test_extract_disk_id(self):
        mock_tool = Mock()
        mock_tool.args = {
            "diskId": {"type": "string"},
        }

        context = "磁盘ID为 d-xyz789uvw，请检查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "diskId": "d-xyz789uvw"
        }
        self.assertEqual(result, expected)

    def test_extract_nc_ip(self):
        mock_tool = Mock()
        mock_tool.args = {
            "ncIp": {"type": "string"},
        }

        context = "宿主机IP地址为 *************，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "ncIp": "*************"
        }
        self.assertEqual(result, expected)

    def test_extract_invalid_param_name(self):
        mock_tool = Mock()
        mock_tool.args = {
            "invalidParam": {"type": "string"},
        }

        context = "some context"
        result = self.manager.extract_param(context, mock_tool)
        self.assertIsNone(result)

    def test_extract_no_matches(self):
        mock_tool = Mock()
        mock_tool.args = {
            "instanceId": {"type": "string"},
        }

        context = "没有实例ID"
        result = self.manager.extract_param(context, mock_tool)
        self.assertIsNone(result)

    def test_extract_multiple_matches(self):
        mock_tool = Mock()
        mock_tool.args = {
            "instanceId": {"type": "string"},
        }

        context = "实例ID i-abc123 和 i-def456 都存在"
        result = self.manager.extract_param(context, mock_tool)
        self.assertIsNone(result)  # Should return None when multiple matches found

    def test_extract_instance_id_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "instanceIds": {
                "type": "array"
            }
        }

        context = "实例ID为 i-wz9b873bysppltjo2qpe，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "instanceIds": ["i-wz9b873bysppltjo2qpe"]
        }
        self.assertEqual(result, expected)

    def test_extract_event_id_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "eventIds": {
                "type": "array"
            }
        }

        context = "事件ID为 e-abc123def，请处理"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "eventIds": ["e-abc123def"]
        }
        self.assertEqual(result, expected)

    def test_extract_disk_id_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "diskIds": {
                "type": "array"
            }
        }

        context = "磁盘ID为 d-xyz789uvw，请检查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "diskIds": ["d-xyz789uvw"]
        }
        self.assertEqual(result, expected)

    def test_extract_uid_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "uids": {
                "type": "array"
            }
        }

        context = "用户ID为 1234567，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "uids": ["1234567"]
        }
        self.assertEqual(result, expected)

    def test_extract_machine_id_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "machineIds": {
                "type": "array"
            }
        }

        context = "机器ID为 12345-6789，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "machineIds": ["12345-6789"]
        }
        self.assertEqual(result, expected)

    def test_extract_nc_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "ncs": {
                "type": "array"
            }
        }

        context = "宿主机为 12345-6789，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "ncs": ["12345-6789"]
        }
        self.assertEqual(result, expected)

    def test_extract_nc_ip_list(self):
        mock_tool = Mock()
        mock_tool.args = {
            "ncIps": {
                "type": "array"
            }
        }

        context = "宿主机IP地址为 *************，请核查"
        result = self.manager.extract_param(context, mock_tool)

        expected = {
            "ncIps": ["*************"]
        }
        self.assertEqual(result, expected)

    def test_sanitize_content_with_valid_markdown_link(self):
        content = "更多信息请访问[官方网站](https://www.example.com)。"
        context = "https://www.example.com"
        result = self.manager.sanitize_content(content, context)
        self.assertEqual(result, content)  # Should remain unchanged

    def test_sanitize_content_with_invalid_markdown_link(self):
        content = "更多信息请访问[官方网站](https://www.example.com)。"
        context = "some other context"
        result = self.manager.sanitize_content(content, context)
        self.assertEqual(result, "更多信息请访问 ~~[官方网站](https://www.example.com)~~ 。")

    def test_sanitize_content_with_valid_plain_url(self):
        content = "访问 https://www.example.com 获取更多信息。"
        context = "https://www.example.com"
        result = self.manager.sanitize_content(content, context)
        self.assertEqual(result, content)  # Should remain unchanged

    def test_sanitize_content_with_invalid_plain_url(self):
        content = "访问 https://www.example.com 获取更多信息。"
        context = "some other context"
        result = self.manager.sanitize_content(content, context)
        self.assertEqual(result, "访问  ~~https://www.example.com~~  获取更多信息。")

    def test_sanitize_content_with_mixed_urls(self):
        content = "请访问[官方网站](https://www.example.com)或 https://www.another.com 获取更多信息。"
        context = "https://www.example.com"
        result = self.manager.sanitize_content(content, context)
        self.assertEqual(result, "请访问[官方网站](https://www.example.com)或  ~~https://www.another.com~~  获取更多信息。")

    def test_sanitize_content_with_invalid_markdown_link_and_plain_url(self):
        content = "访问[非法链接](https://www.bad.com)或https://www.bad.com获取更多信息。"
        context = "some other context"
        result = self.manager.sanitize_content(content, context)
        # 确保不会出现重复的删除线
        self.assertEqual(result, "访问 ~~[非法链接](https://www.bad.com)~~ 或 ~~https://www.bad.com~~ 获取更多信息。")


if __name__ == '__main__':
    unittest.main()
