<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 优化后的配色方案 -->
    <linearGradient id="grad-header" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="grad-roof" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="grad-interface" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;"/><stop offset="100%" style="stop-color:#059669;"/></linearGradient>
    <linearGradient id="grad-agent" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="grad-foundation" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#64748b;"/><stop offset="100%" style="stop-color:#475569;"/></linearGradient>
    <radialGradient id="grad-sky" cx="50%" cy="20%" r="80%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#eef2ff;"/></radialGradient>
    <linearGradient id="grad-highlight" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f97316;"/><stop offset="100%" style="stop-color:#f59e0b;"/></linearGradient>
    
    <!-- 更细腻的阴影效果 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-color="#1e293b" flood-opacity="0.1"/>
    </filter>
    <filter id="strong-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="5" dy="5" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.15"/>
    </filter>
  </defs>

  <!-- 整体容器与背景 -->
  <g>
    <rect x="0" y="0" width="1800" height="1250" fill="url(#grad-sky)"/>

    <!-- 装饰元素 -->
    <g opacity="0.6">
        <ellipse cx="200" cy="150" rx="60" ry="35" fill="white"/>
        <ellipse cx="230" cy="140" rx="45" ry="28" fill="white"/>
        <ellipse cx="1600" cy="170" rx="70" ry="40" fill="white"/>
        <ellipse cx="1635" cy="160" rx="50" ry="30" fill="white"/>
        <circle cx="1550" cy="120" r="40" fill="#fde047" opacity="0.3"/>
        <circle cx="1550" cy="120" r="30" fill="#fde047" opacity="0.4"/>
    </g>
    
    <!-- 标题栏 -->
    <rect x="0" y="0" width="1800" height="70" fill="url(#grad-header)" filter="url(#strong-shadow)"/>
    <text x="900" y="42" text-anchor="middle" fill="white" font-size="28" font-weight="bold" letter-spacing="1" font-family="'Inter', 'Segoe UI', sans-serif">CloudBot 智能体技术架构</text>

    <!-- 主体架构容器 -->
    <g transform="translate(100, -10)">

        <!-- 业务层 (屋顶) -->
        <g id="business-roof">
          <polygon points="50,380 350,110 1250,110 1550,380" fill="url(#grad-roof)" filter="url(#strong-shadow)"/>
          <text x="800" y="150" text-anchor="middle" fill="white" font-size="26" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">业务层 - 核心业务目标</text>

          <rect x="420" y="180" width="760" height="30" fill="rgba(255,255,255,0.9)" rx="15" stroke="#93c5fd" stroke-width="1"/>
          <text x="800" y="200" text-anchor="middle" fill="#1e40af" font-size="14" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">🎯 总体目标: 通过AI智能体实现运维服务无人化、诊断服务规模化、测试流程智能化</text>

          <g transform="translate(0, 10)">
            <!-- Box 1: 工单自动化 -->
            <g transform="translate(-15, 0)">
              <rect x="250" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="425" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">工单自动化</text>
              <line x1="270" y1="280" x2="580" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="265" y="298" fill="#334155" font-size="12" font-weight="bold">技术指标:</text>
              <text x="275" y="315" fill="#475569" font-size="11">• 诊断准确率: <tspan style="fill:#1d4ed8; font-weight:bold;">40% → 60%</tspan></text>
              <text x="435" y="315" fill="#475569" font-size="11">• 根因覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">>80%</tspan></text>
              <text x="275" y="332" fill="#475569" font-size="11">• 分类/任务拆解准确率: <tspan style="fill:#1d4ed8; font-weight:bold;">>X%</tspan></text>
              <text x="275" y="349" fill="#475569" font-size="11">• 知识库/SOP覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">>X%</tspan></text>
            </g>

            <!-- Box 2: 智能诊断助手 -->
            <g transform="translate(0, 0)">
              <rect x="625" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="800" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">智能诊断助手</text>
              <line x1="645" y1="280" x2="955" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="640" y="298" fill="#334155" font-size="12" font-weight="bold">关键指标: • 日均UV <tspan style="fill:#1d4ed8; font-weight:bold;">10倍+ (500+)</tspan></text>
              <text x="640" y="315" fill="#334155" font-size="12" font-weight="bold">关键能力 (Milestones):</text>
              <text x="650" y="332" fill="#475569" font-size="11">• M1/M4: DingOps &amp; 交互式诊断</text>
              <text x="650" y="349" fill="#475569" font-size="11">• M2/M3: CIPU &amp; 长推理/报告</text>
            </g>

            <!-- Box 3: 测试智能化 -->
            <g transform="translate(15, 0)">
              <rect x="1000" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="1175" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">测试智能化</text>
              <line x1="1020" y1="280" x2="1330" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="1015" y="300" fill="#b45309" font-size="12" font-weight="bold">价值: • 赋能测试, AI智能生成</text>
              <text x="1015" y="320" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
              <text x="1025" y="340" fill="#475569" font-size="12">• 自动化覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">+30%</tspan></text>
              <text x="1185" y="340" fill="#475569" font-size="12">• 测试构建效率: <tspan style="fill:#1d4ed8; font-weight:bold;">+50%</tspan></text>
            </g>
          </g>
        </g>
        
        <path d="M 800 380 V 395" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- 接口层 (上层) -->
        <g id="interface-floor">
          <rect x="50" y="395" width="1500" height="265" fill="url(#grad-interface)" rx="15" filter="url(#strong-shadow)"/>
          <text x="800" y="425" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">接口层 - 智能融入各大平台</text>

          <g>
              <!-- Box 1: 钉钉DingOps -->
              <g transform="translate(-74, 0)">
                <rect x="200" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="340" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">钉钉DingOps</text>
                <text x="215" y="490" fill="#b45309" font-size="11">价值: • 移动端接入，扩大用户群体</text>
                <text x="215" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="225" y="527" fill="#475569" font-size="11">• First Token 延迟: &lt;10s</text>
                <text x="225" y="542" fill="#475569" font-size="11">• 日均使用量 > 50</text>
                <rect x="215" y="560" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="576" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">故障、异常诊断场景接入</text>
                <rect x="215" y="590" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="606" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">CloudBot诊断用户群聊</text>
                <rect x="215" y="620" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="636" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">工单和线上问题通知闭环能力</text>
              </g>

              <!-- Box 2: Aone平台集成 -->
              <g transform="translate(-18, 0)">
                <rect x="500" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="640" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">Aone平台集成</text>
                <text x="515" y="490" fill="#b45309" font-size="11">价值: • 实时工单信息推送能力</text>
                <text x="515" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="525" y="527" fill="#475569" font-size="11">• Aone 推送场景：&gt; 3个</text>
                <rect x="515" y="575" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="640" y="592" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Aone 自动Comment 能力</text>
                <rect x="515" y="610" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="640" y="627" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Aone @CloudBot自动回复能力</text>
              </g>
              
              <!-- Box 3: AES客户端智能诊断 -->
              <g transform="translate(38, 0)">
                <rect x="800" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="940" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">AES客户端智能诊断</text>
                <text x="815" y="490" fill="#b45309" font-size="11">价值: • 一键诊断能力</text>
                <text x="815" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="825" y="527" fill="#475569" font-size="11">• 覆盖率: +60%</text>
                <text x="825" y="542" fill="#475569" font-size="11">• 采纳率: +60%</text>
                <rect x="815" y="575" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="940" y="592" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">用户触发诊断能力</text>
                <rect x="815" y="610" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="940" y="627" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">自主通知诊断报告</text>
              </g>

              <!-- Box 4: CloudBot UI诊断页面 -->
              <g transform="translate(94, 0)">
                <rect x="1100" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="1240" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">CloudBot UI诊断页面</text>
                <text x="1115" y="490" fill="#b45309" font-size="11">价值: • CloudBot平台智能化</text>
                <text x="1115" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="1125" y="527" fill="#475569" font-size="11">• 用户使用量: >300 /日</text>
                <rect x="1115" y="560" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="576" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">多视角(研发侧/客户侧)智能诊断</text>
                <rect x="1115" y="590" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="606" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Clientops客户诊断</text>
                <rect x="1115" y="620" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="636" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">深度探索诊断能力</text>
              </g>
          </g>
        </g>
        
        <path d="M 800 660 V 675" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- Agent层 (主层) -->
        <g id="agent-floor">
          <rect x="50" y="675" width="1500" height="265" fill="url(#grad-agent)" rx="15" filter="url(#strong-shadow)"/>
          <text x="800" y="705" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">Agent层 - 多Agent能力</text>

          <g>
              <!-- Box 1 -->
              <g transform="translate(-74, 0)">
                <rect x="200" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="340" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">Multi Agent Routing</text>
                <text x="215" y="770" fill="#b45309" font-size="11">价值: • 智能分发，提升系统效率</text>
                <text x="215" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="225" y="807" fill="#475569" font-size="11">• 路由准确率: 95%+</text>
                <text x="225" y="822" fill="#475569" font-size="11">• 响应时延: &lt;2s</text>
                <rect x="215" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="856" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求路由不同智能体</text>
                <rect x="215" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="886" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求改写</text>
                <rect x="215" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="916" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求工具推荐</text>
              </g>

              <!-- Box 2 -->
              <g transform="translate(-18, 0)">
                <rect x="500" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="640" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">交互诊断</text>
                <text x="515" y="770" fill="#b45309" font-size="11">价值: • 赋能产研/TAM，提供专家级对话</text>
                <text x="515" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="525" y="807" fill="#475569" font-size="11">• 诊断准确率: >60%</text>
                <text x="525" y="822" fill="#475569" font-size="11">• 覆盖场景数: >10</text>
                <rect x="515" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="856" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">多轮对话支持</text>
                <rect x="515" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="886" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">复杂任务Plan &amp; Solve的诊断能力</text>
                <rect x="515" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="916" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">知识库RAG能力</text>
              </g>

              <!-- Box 3 -->
              <g transform="translate(38, 0)">
                <rect x="800" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="940" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">推理诊断</text>
                <text x="815" y="770" fill="#b45309" font-size="11">价值: • 赋能产研，提供实例级诊断报告</text>
                <text x="815" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="825" y="807" fill="#475569" font-size="11">• 诊断准确率: >60%</text>
                <text x="825" y="822" fill="#475569" font-size="11">• 覆盖场景数: >10</text>
                <rect x="815" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="856" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">支持多实例、多时间点诊断任务</text>
                <rect x="815" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="886" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">支持多场景复杂任务拆解</text>
                <rect x="815" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="916" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">数据分析和处理能力</text>
              </g>

              <!-- Box 4 -->
              <g transform="translate(94, 0)">
                <rect x="1100" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="1240" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">测试智能体</text>
                <text x="1115" y="770" fill="#b45309" font-size="11">价值: • 赋能测试, 提升软件质量与效率</text>
                <text x="1115" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="1125" y="807" fill="#475569" font-size="11">• 自动化覆盖率: +30%</text>
                <text x="1125" y="822" fill="#475569" font-size="11">• 测试构建效率: +50%</text>
                <rect x="1115" y="855" width="250" height="28" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="1240" y="872" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">智能用例生成</text>
                <rect x="1115" y="890" width="250" height="28" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="1240" y="907" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">自动化编排</text>
              </g>
          </g>
        </g>
        
        <path d="M 800 940 V 955" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- 技术底座层 (地基) -->
        <g id="foundation">
          <rect x="25" y="955" width="1550" height="250" fill="url(#grad-foundation)" rx="15" filter="url(#strong-shadow)"/>
          <text x="800" y="985" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">技术底座层 - 夯实基础技术</text>
          
          <g transform="translate(0, 10)">
              <!-- Dashed boxes for grouping -->
              <rect x="50" y="1000" width="750" height="155" rx="10" fill="none" stroke="#3b82f6" stroke-width="2" stroke-dasharray="5,5"/>
              <text x="425" y="995" text-anchor="middle" fill="white" font-size="16" font-weight="bold">Agent架构底座</text>

              <rect x="800" y="1000" width="500" height="155" rx="10" fill="none" stroke="#10b981" stroke-width="2" stroke-dasharray="5,5"/>
              <text x="1050" y="995" text-anchor="middle" fill="white" font-size="16" font-weight="bold">工程化</text>

              <rect x="1300" y="1000" width="250" height="155" rx="10" fill="none" stroke="#8b5cf6" stroke-width="2" stroke-dasharray="5,5"/>
              <text x="1425" y="995" text-anchor="middle" fill="white" font-size="16" font-weight="bold">对外合作</text>

              <!-- Agent大脑: 规划与推理 -->
              <g transform="translate(0, 0)">
                <rect x="60" y="1020" width="230" height="115" fill="#eff6ff" rx="10" stroke="#93c5fd" stroke-width="1" filter="url(#shadow)"/>
                <text x="175" y="1043" text-anchor="middle" fill="#1e40af" font-size="16" font-weight="bold">Agent大脑：规划与推理</text>
                <rect x="70" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="120" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Iterative Planning</text><text x="120" y="1083" text-anchor="middle" fill="#475569" font-size="8">迭代式任务规划</text>
                <rect x="180" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="230" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Multi-Agent Routing</text><text x="230" y="1083" text-anchor="middle" fill="#475569" font-size="8">智能体调度中枢</text>
                <rect x="70" y="1095" width="210" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="175" y="1110" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">No SOP Reasoning</text><text x="175" y="1123" text-anchor="middle" fill="#475569" font-size="8">零样本智能推理</text>
              </g>

              <!-- 知识与记忆: 信息与上下文 -->
              <g transform="translate(0, 0)">
                <rect x="310" y="1020" width="230" height="115" fill="#f0fdf4" rx="10" stroke="#a7f3d0" stroke-width="1" filter="url(#shadow)"/>
                <text x="425" y="1043" text-anchor="middle" fill="#065f46" font-size="16" font-weight="bold">知识与记忆：信息与上下文</text>
                <rect x="320" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="370" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Agent Memory</text><text x="370" y="1083" text-anchor="middle" fill="#475569" font-size="8">长短期记忆系统</text>
                <rect x="430" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="480" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Knowledge Graph</text><text x="480" y="1083" text-anchor="middle" fill="#475569" font-size="8">知识图谱存储</text>
                <rect x="320" y="1095" width="210" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="425" y="1110" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">SOP System</text><text x="425" y="1123" text-anchor="middle" fill="#475569" font-size="8">SOP体系化建设</text>
              </g>
              
              <!-- 模型与执行: 基础能力与安全 -->
              <g transform="translate(0, 0)">
                <rect x="560" y="1020" width="230" height="115" fill="#f5f3ff" rx="10" stroke="#c4b5fd" stroke-width="1" filter="url(#shadow)"/>
                <text x="675" y="1043" text-anchor="middle" fill="#6d28d9" font-size="16" font-weight="bold">模型与执行：基础能力与安全</text>
                <rect x="570" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="620" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Agent Fine-tuning</text><text x="620" y="1083" text-anchor="middle" fill="#475569" font-size="8">智能体微调优化</text>
                <rect x="680" y="1055" width="100" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="730" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">CodeAgent</text><text x="730" y="1083" text-anchor="middle" fill="#475569" font-size="8">抑制幻觉</text>
                <rect x="570" y="1095" width="210" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="675" y="1110" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">Sandbox Environment</text><text x="675" y="1123" text-anchor="middle" fill="#475569" font-size="8">安全隔离执行</text>
              </g>

              <!-- 基础工程化能力 -->
              <g transform="translate(0, 0)">
                <rect x="810" y="1020" width="230" height="115" fill="#fffbeb" rx="10" stroke="#f59e0b" stroke-width="1" filter="url(#shadow)"/>
                <text x="925" y="1043" text-anchor="middle" fill="#b45309" font-size="16" font-weight="bold">基础工程化能力</text>
                <rect x="820" y="1055" width="210" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="925" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">OpenAPI转化MCP工具、跟踪能力</text>
                <rect x="820" y="1095" width="210" height="35" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="925" y="1110" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">服务可观测性</text>
              </g>

              <!-- 工单数据处理 -->
              <g transform="translate(0, 0)">
                <rect x="1060" y="1020" width="230" height="115" fill="#fef2f2" rx="10" stroke="#fca5a5" stroke-width="1" filter="url(#shadow)"/>
                <text x="1175" y="1043" text-anchor="middle" fill="#b91c1c" font-size="16" font-weight="bold">工单数据处理</text>
                <rect x="1070" y="1055" width="210" height="25" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="1175" y="1070" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">工单采集</text>
                <rect x="1070" y="1082" width="210" height="25" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="1175" y="1097" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">工单分类</text>
                <rect x="1070" y="1109" width="210" height="25" fill="#f8fafc" rx="5" stroke="#e2e8f0" stroke-width="1"/><text x="1175" y="1124" text-anchor="middle" fill="#334155" font-size="10" font-weight="bold">工单关单SOP知识库构建</text>
              </g>

              <!-- 团队协作生态 -->
              <g transform="translate(0, 0)">
                <rect x="1310" y="1020" width="230" height="115" fill="#f3f4f6" rx="10" stroke="#d1d5db" stroke-width="1" filter="url(#shadow)"/>
                <text x="1425" y="1043" text-anchor="middle" fill="#4b5563" font-size="16" font-weight="bold">团队协作</text>
                <rect x="1320" y="1055" width="210" height="25" fill="#e5e7eb" rx="5" stroke="#d1d5db" stroke-width="1"/>
                <text x="1425" y="1070" text-anchor="middle" fill="#374151" font-size="11" font-weight="bold">热迁移MCP 复用</text>
                <rect x="1320" y="1082" width="210" height="25" fill="#e5e7eb" rx="5" stroke="#d1d5db" stroke-width="1"/>
                <text x="1425" y="1097" text-anchor="middle" fill="#374151" font-size="11" font-weight="bold">ChatBI能力复用</text>
                <rect x="1320" y="1109" width="210" height="25" fill="#e5e7eb" rx="5" stroke="#d1d5db" stroke-width="1"/>
                <text x="1425" y="1124" text-anchor="middle" fill="#374151" font-size="11" font-weight="bold">知识库复用</text>
              </g>
          </g>
        </g>
    </g>
  </g>
</svg>