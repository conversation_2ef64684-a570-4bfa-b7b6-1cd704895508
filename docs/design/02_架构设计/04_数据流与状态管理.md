# 数据流与状态管理

## 1. 概述

ECS 深度诊断系统采用基于 LangGraph 的状态管理机制，通过 `ReasoningState` 统一管理多智能体协同过程中的数据流转和状态变更。系统支持状态持久化、版本控制和并发访问。

## 2. 状态模型

### 2.1 核心状态定义

#### 📊 ReasoningState 结构
```python
class ReasoningState(MessagesState):
    """推理状态 - 继承LangGraph的MessagesState"""
    
    # === 身份标识 ===
    request_id: str = ""                    # 请求唯一标识符
    
    # === 执行状态 ===
    observations: List[str] = []            # 执行观察记录
    plan_iterations: int = 0                # 计划迭代次数
    current_plan: Plan | str = None         # 当前执行计划
    final_report: str = ""                  # 最终报告内容
    
    # === 控制参数 ===
    auto_accepted_plan: bool = False        # 自动接受计划标志
    enable_background_investigation: bool = True  # 启用背景调查
    
    # === 数据存储 ===
    background_investigation_results: str = None  # 背景调查结果
    mcp_servers_description: str = ""       # MCP服务器描述
    sop_name: str = ""                     # 使用的SOP名称
    
    # === HTML报告管理 ===
    reports: List[ReportState] = []         # 历史报告版本
    problem_description_html: str = ""      # 问题描述HTML片段
    diagnosis_info_html: str = ""          # 诊断信息HTML片段
    key_findings_html: str = ""            # 关键发现HTML片段
    evidence_chain_html: str = ""          # 证据链HTML片段
    summary_conclusion_html: str = ""      # 总结结论HTML片段
    merged_html_report: str = ""           # 合并后的完整报告
```

#### 📋 ReportState 结构
```python
@dataclass
class ReportState:
    """单次诊断报告状态（版本化）"""
    
    report_html: str = ""                  # 报告HTML内容
    iteration: int = 1                     # 迭代版本号
    generator: str = ""                    # 生成器标识
    
    # 验证状态
    validate_status: Literal["pending", "passed", "failed"] = "pending"
    reflection_status: Literal["pending", "passed", "failed"] = "pending"
    
    # 错误和反馈
    validate_error: Union[dict, str, None] = None
    reflection_problems: List[str] = []
    generation_feedback: dict = {}
    
    # 存储信息
    url: Optional[str] = None              # 报告访问URL
    path: Optional[str] = None             # 文件存储路径
```

### 2.2 状态继承关系

#### 🔗 MessagesState 基础
```python
# ReasoningState 继承自 LangGraph 的 MessagesState
# 自动获得消息管理能力
class MessagesState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]

# 因此 ReasoningState 自动包含：
# - messages: 消息历史记录
# - add_messages: 消息累加器函数
```

## 3. 数据流设计

### 3.1 状态流转模式

#### 🔄 标准流转模式
```mermaid
stateDiagram-v2
    [*] --> 初始化状态
    初始化状态 --> 协调阶段: coordinator
    协调阶段 --> 背景调查: enable_background_investigation=true
    协调阶段 --> 规划阶段: enable_background_investigation=false
    背景调查 --> 规划阶段: background_investigation_results
    规划阶段 --> 人工反馈: current_plan
    人工反馈 --> 规划阶段: plan rejected
    人工反馈 --> 执行阶段: plan accepted
    执行阶段 --> 执行阶段: step execution
    执行阶段 --> 报告生成: all steps completed
    报告生成 --> 并行HTML生成: final_report
    并行HTML生成 --> HTML合并: html fragments
    HTML合并 --> [*]: merged_html_report
```

#### 📝 状态更新流程
```python
# 智能体通过Command机制更新状态
async def agent_execution_example(state: ReasoningState, config: RunnableConfig) -> Command:
    """智能体执行示例"""
    
    # 1. 读取当前状态
    current_plan = state.get("current_plan")
    plan_iterations = state.get("plan_iterations", 0)
    
    # 2. 执行业务逻辑
    new_plan = await generate_new_plan(state)
    
    # 3. 返回状态更新命令
    return Command(
        update={
            "current_plan": new_plan,
            "plan_iterations": plan_iterations + 1,
            "observations": state.get("observations", []) + [f"Generated plan iteration {plan_iterations + 1}"]
        },
        goto="next_agent"
    )
```

### 3.2 数据传递机制

#### 📤 输入数据流
```python
async def process_input_data(question: str, **kwargs) -> dict:
    """处理输入数据"""
    
    # 构建初始状态
    input_data = {
        "messages": [{"role": "user", "content": question}],
        "request_id": kwargs.get("request_id", str(uuid4())),
        "auto_accepted_plan": kwargs.get("auto_accepted_plan", True),
        "enable_background_investigation": kwargs.get("enable_background_investigation", True),
        "observations": [],
        "plan_iterations": 0
    }
    
    return input_data
```

#### 📥 输出数据流
```python
async def extract_output_data(final_state: ReasoningState) -> dict:
    """提取输出数据"""
    
    return {
        "request_id": final_state.get("request_id"),
        "final_report": final_state.get("final_report", ""),
        "merged_html_report": final_state.get("merged_html_report", ""),
        "execution_summary": {
            "plan_iterations": final_state.get("plan_iterations", 0),
            "sop_used": final_state.get("sop_name", ""),
            "total_observations": len(final_state.get("observations", []))
        },
        "reports_history": final_state.get("reports", [])
    }
```

## 4. 状态管理机制

### 4.1 状态持久化

#### 💾 检查点存储
```python
from langgraph.checkpoint.memory import MemorySaver

def build_graph_with_memory():
    """构建带持久化的工作流图"""
    
    # LangGraph内置内存存储
    memory = MemorySaver()
    
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)

# 状态自动持久化到检查点
async def execute_with_persistence(input_data: dict, thread_id: str):
    """带持久化的执行"""
    
    graph = build_graph_with_memory()
    config = {"configurable": {"thread_id": thread_id}}
    
    # 状态会自动保存到检查点
    async for event in graph.astream(input_data, config=config):
        yield event
```

#### 🔄 状态恢复
```python
async def resume_from_checkpoint(thread_id: str, checkpoint_id: Optional[str] = None):
    """从检查点恢复执行"""
    
    graph = build_graph_with_memory()
    config = {"configurable": {"thread_id": thread_id}}
    
    if checkpoint_id:
        config["configurable"]["checkpoint_id"] = checkpoint_id
    
    # 从指定检查点继续执行
    async for event in graph.astream(None, config=config):
        yield event
```

### 4.2 并发控制

#### 🔒 状态一致性
```python
class StateConsistencyManager:
    """状态一致性管理器"""
    
    def __init__(self):
        self.locks = {}
    
    async def acquire_state_lock(self, request_id: str):
        """获取状态锁"""
        if request_id not in self.locks:
            self.locks[request_id] = asyncio.Lock()
        return self.locks[request_id]
    
    async def update_state_safely(self, request_id: str, updates: dict):
        """安全更新状态"""
        async with await self.acquire_state_lock(request_id):
            # 原子性状态更新
            return await self._apply_updates(updates)
```

#### ⚡ 并发访问优化
```python
async def parallel_state_access(state: ReasoningState, read_only_operations: List[Callable]):
    """并发只读状态访问"""
    
    # 只读操作可以并发执行
    tasks = [operation(state) for operation in read_only_operations]
    results = await asyncio.gather(*tasks)
    
    return results

async def sequential_state_modification(state: ReasoningState, write_operations: List[Callable]):
    """顺序状态修改"""
    
    # 写操作必须顺序执行
    results = []
    for operation in write_operations:
        result = await operation(state)
        results.append(result)
    
    return results
```

## 5. 事件驱动状态更新

### 5.1 状态变更事件

#### 📡 事件发布机制
```python
class StateChangeEvent:
    """状态变更事件"""
    
    def __init__(self, request_id: str, agent: str, changes: dict, timestamp: datetime):
        self.request_id = request_id
        self.agent = agent
        self.changes = changes
        self.timestamp = timestamp

class StateEventPublisher:
    """状态事件发布器"""
    
    def __init__(self, redis_client: Redis):
        self.redis = redis_client
    
    async def publish_state_change(self, event: StateChangeEvent):
        """发布状态变更事件"""
        
        channel = f"state_changes:{event.request_id}"
        await self.redis.publish(channel, json.dumps({
            "agent": event.agent,
            "changes": event.changes,
            "timestamp": event.timestamp.isoformat()
        }))
```

#### 📨 事件订阅处理
```python
class StateEventSubscriber:
    """状态事件订阅器"""
    
    async def subscribe_state_changes(self, request_id: str, callback: Callable):
        """订阅状态变更"""
        
        channel = f"state_changes:{request_id}"
        pubsub = self.redis.pubsub()
        await pubsub.subscribe(channel)
        
        async for message in pubsub.listen():
            if message["type"] == "message":
                event_data = json.loads(message["data"])
                await callback(event_data)
```

### 5.2 HTML报告状态管理

#### 📊 报告版本控制
```python
async def add_report_version(state: ReasoningState, new_report: str, generator: str) -> ReasoningState:
    """添加新的报告版本"""
    
    reports = state.get("reports", [])
    
    # 创建新版本
    new_version = ReportState(
        report_html=new_report,
        iteration=len(reports) + 1,
        generator=generator,
        validate_status="pending"
    )
    
    reports.append(new_version)
    
    return Command(
        update={"reports": reports},
        goto="validation"
    )
```

#### 🔄 并发HTML片段管理
```python
async def update_html_fragments(state: ReasoningState, fragments: dict) -> Command:
    """更新HTML片段"""
    
    updates = {}
    
    # 更新各个HTML片段
    if "problem_description" in fragments:
        updates["problem_description_html"] = fragments["problem_description"]
    
    if "diagnosis_info" in fragments:
        updates["diagnosis_info_html"] = fragments["diagnosis_info"]
    
    if "key_findings" in fragments:
        updates["key_findings_html"] = fragments["key_findings"]
    
    if "evidence_chain" in fragments:
        updates["evidence_chain_html"] = fragments["evidence_chain"]
    
    if "summary_conclusion" in fragments:
        updates["summary_conclusion_html"] = fragments["summary_conclusion"]
    
    return Command(update=updates, goto="html_report_merger")
```

## 6. 状态监控与调试

### 6.1 状态快照

#### 📸 状态快照机制
```python
class StateSnapshot:
    """状态快照"""
    
    def __init__(self, state: ReasoningState, agent: str, timestamp: datetime):
        self.state_data = dict(state)
        self.agent = agent
        self.timestamp = timestamp
        self.size = len(json.dumps(self.state_data))
    
    def get_summary(self) -> dict:
        """获取状态摘要"""
        return {
            "agent": self.agent,
            "timestamp": self.timestamp.isoformat(),
            "size_bytes": self.size,
            "request_id": self.state_data.get("request_id"),
            "plan_iterations": self.state_data.get("plan_iterations", 0),
            "observations_count": len(self.state_data.get("observations", [])),
            "reports_count": len(self.state_data.get("reports", []))
        }

class StateSnapshotManager:
    """状态快照管理器"""
    
    def __init__(self):
        self.snapshots = {}
    
    def capture_snapshot(self, request_id: str, state: ReasoningState, agent: str):
        """捕获状态快照"""
        if request_id not in self.snapshots:
            self.snapshots[request_id] = []
        
        snapshot = StateSnapshot(state, agent, datetime.now())
        self.snapshots[request_id].append(snapshot)
    
    def get_execution_timeline(self, request_id: str) -> List[dict]:
        """获取执行时间线"""
        snapshots = self.snapshots.get(request_id, [])
        return [snapshot.get_summary() for snapshot in snapshots]
```

### 6.2 性能监控

#### 📊 状态性能指标
```python
class StatePerformanceMonitor:
    """状态性能监控器"""
    
    def __init__(self):
        self.metrics = {
            "state_size_history": [],
            "update_frequency": {},
            "agent_state_usage": {}
        }
    
    def record_state_size(self, request_id: str, size: int, agent: str):
        """记录状态大小"""
        self.metrics["state_size_history"].append({
            "request_id": request_id,
            "size": size,
            "agent": agent,
            "timestamp": datetime.now()
        })
    
    def record_state_update(self, request_id: str, agent: str):
        """记录状态更新"""
        key = f"{request_id}:{agent}"
        self.metrics["update_frequency"][key] = self.metrics["update_frequency"].get(key, 0) + 1
    
    def get_performance_summary(self, request_id: str) -> dict:
        """获取性能摘要"""
        size_data = [m for m in self.metrics["state_size_history"] if m["request_id"] == request_id]
        
        return {
            "max_state_size": max([m["size"] for m in size_data]) if size_data else 0,
            "avg_state_size": sum([m["size"] for m in size_data]) / len(size_data) if size_data else 0,
            "total_updates": len(size_data),
            "agents_involved": list(set([m["agent"] for m in size_data]))
        }
```

## 7. 最佳实践

### 7.1 状态设计原则
1. **最小化状态**：只保存必要的状态数据
2. **结构化数据**：使用结构化数据模型
3. **版本控制**：支持状态版本和回滚
4. **并发安全**：确保并发访问的安全性

### 7.2 性能优化
1. **懒加载**：按需加载大型状态数据
2. **增量更新**：使用增量更新减少开销
3. **压缩存储**：压缩持久化状态数据
4. **缓存策略**：缓存频繁访问的状态

### 7.3 调试技巧
1. **状态快照**：定期捕获状态快照
2. **变更日志**：记录所有状态变更
3. **性能监控**：监控状态大小和更新频率
4. **错误恢复**：支持从任意检查点恢复