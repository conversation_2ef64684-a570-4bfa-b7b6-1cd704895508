# 多智能体协同架构设计

## 1. 概述

ECS 深度诊断系统采用基于 LangGraph 的多智能体协同架构，通过专业化智能体分工协作，实现复杂诊断任务的自动化处理。系统基于 `ReasoningState` 状态管理，通过标准化的 `Command` 机制实现智能体间的协调。

## 2. 核心架构

### 2.1 基础架构组件

#### 📋 状态管理
```python
class ReasoningState(MessagesState):
    """推理状态 - 系统的核心状态容器"""
    
    # 基础标识
    request_id: str = ""                    # 请求唯一标识
    
    # 执行状态  
    observations: List[str] = []            # 观察记录
    plan_iterations: int = 0                # 计划迭代次数
    current_plan: Plan | str = None         # 当前执行计划
    final_report: str = ""                  # 最终报告
    
    # 控制参数
    auto_accepted_plan: bool = False        # 自动接受计划
    enable_background_investigation: bool = True  # 启用背景调查
    
    # 数据存储
    background_investigation_results: str = None  # 背景调查结果
    mcp_servers_description: str = ""       # MCP服务器描述
    sop_name: str = ""                     # SOP名称
    
    # HTML报告管理
    reports: List[ReportState] = []         # 报告版本列表
    # 并发HTML片段
    problem_description_html: str = ""      # 问题描述HTML
    diagnosis_info_html: str = ""          # 诊断信息HTML  
    key_findings_html: str = ""            # 关键发现HTML
    evidence_chain_html: str = ""          # 证据链HTML
    summary_conclusion_html: str = ""      # 总结结论HTML
    merged_html_report: str = ""           # 合并后的HTML报告
```

#### 🤖 智能体基类
```python
class BaseAgent(ABC):
    """统一的智能体基类"""
    
    def __init__(self, name: str, agent_type: str, role: str = None):
        self.name = name                    # 智能体名称
        self.agent_type = agent_type        # 智能体类型
        self.role = role or agent_type      # 角色描述
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    async def execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        """统一执行入口"""
        try:
            self.logger.info(f"{self.name} starting")
            result = await self._do_execute(state, config)
            self.logger.info(f"{self.name} completed")
            return result
        except Exception as e:
            self.logger.error(f"{self.name} error: {e}")
            return self._handle_error(e, state)
    
    @abstractmethod
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        """子类实现具体逻辑"""
        pass
```

### 2.2 工作流架构

#### 🔄 LangGraph 工作流构建
```python
def _build_base_graph():
    """构建基础状态图"""
    builder = StateGraph(ReasoningState)
    
    # 添加智能体节点
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("background_investigator", background_investigation_node)
    builder.add_node("planner", planner_node)
    builder.add_node("research_team", research_team_node)
    builder.add_node("researcher", researcher_node)
    builder.add_node("coder", coder_node)
    builder.add_node("human_feedback", human_feedback_node)
    builder.add_node("reporter", reporter_node)
    builder.add_node("parallel_html_generator", parallel_html_generator_node)
    builder.add_node("html_report_merger", html_report_merger_node)
    
    # 定义工作流路径
    builder.add_edge(START, "coordinator")
    builder.add_edge("reporter", "parallel_html_generator")
    builder.add_edge("parallel_html_generator", "html_report_merger")
    builder.add_edge("html_report_merger", END)
    
    return builder
```

## 3. 智能体实现

### 3.1 协调智能体 (Coordinator)

#### 🎯 职责与功能
- **能力匹配**：分析问题并匹配适合的SOP方案
- **任务启动**：启动诊断流程
- **流程控制**：决定是否启用背景调查

#### 💻 实现细节
```python
class CoordinatorAgent(BaseAgent):
    """协调器智能体"""
    
    def __init__(self, config_obj: Configuration):
        super().__init__("coordinator", "coordinator")
        self.sop_service = create_sop_service()
    
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        # 1. 构建SOP能力描述
        state["capabilities"] = await self._build_sop_capabilities()
        
        # 2. 调用LLM分析问题
        messages = apply_prompt_template("agent_coordinator", state)
        response = await get_llm_by_type("coordinator").bind_tools([handoff_to_planner]).ainvoke(messages)
        
        # 3. 决定下一步
        goto = "background_investigator" if state.get("enable_background_investigation") else "planner"
        return Command(goto=goto)
    
    async def _build_sop_capabilities(self) -> str:
        """构建SOP能力描述"""
        sops = await self.sop_service.get_all_sops()
        lines = []
        for sop in sops:
            scenario = sop.scenario.split('\n')[0] if sop.scenario else sop.title
            lines.append(f"- **{sop.title}**: {scenario}")
        return "\n".join(lines)
```

### 3.2 规划智能体 (Planner)

#### 🎯 职责与功能
- **SOP匹配**：根据问题选择合适的SOP
- **计划生成**：生成详细的诊断计划
- **迭代优化**：根据反馈优化计划

#### 💻 实现细节
```python
class PlannerAgent(BaseAgent):
    """规划器智能体"""
    
    def __init__(self, config_obj: Configuration):
        super().__init__("planner", "planner")
        self.sop_service = create_sop_service()
    
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        plan_iterations = state.get("plan_iterations", 0)
        
        # 检查迭代次数限制
        if plan_iterations >= config.max_plan_iterations:
            return Command(goto="reporter")
        
        # 选择和加载SOP
        sop_content, sop_name = await self.sop_service.select_and_load(state)
        
        # 生成计划
        messages = apply_prompt_template("agent_planner", {
            **state, 
            "sop_content": sop_content,
            "sop_name": sop_name
        })
        
        response = await get_llm_by_type("planner").ainvoke(messages)
        
        # 解析计划并更新状态
        plan = self._parse_plan(response.content)
        return Command(
            update={
                "current_plan": plan,
                "plan_iterations": plan_iterations + 1,
                "sop_name": sop_name
            },
            goto="human_feedback"
        )
```

### 3.3 研究团队 (Research Team)

#### 🎯 职责与功能
- **任务分发**：根据步骤类型分发给合适的智能体
- **进度监控**：跟踪执行进度
- **流程控制**：决定下一步行动

#### 💻 实现细节
```python
class ResearchTeamAgent(BaseAgent):
    """研究团队智能体"""
    
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        current_plan = state.get("current_plan")
        
        if not current_plan or not current_plan.steps:
            return Command(goto="planner")
        
        # 检查是否所有步骤完成
        if all(step.execution_res for step in current_plan.steps):
            return Command(goto="planner")
        
        # 路由到合适的智能体
        for step in current_plan.steps:
            if not step.execution_res:
                if step.step_type == StepType.PROCESSING:
                    return Command(goto="coder")
                else:
                    return Command(goto="researcher")
        
        return Command(goto="planner")
```

### 3.4 研究智能体 (Researcher)

#### 🎯 职责与功能
- **工具调用**：执行诊断工具收集数据
- **数据分析**：分析收集到的数据
- **结果整理**：整理分析结果

#### 💻 实现细节
```python
class ResearcherAgent(BaseAgent):
    """研究员智能体"""
    
    def __init__(self):
        super().__init__("researcher", "researcher")
        self.step_executor = StepExecutor(self)
    
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        # 验证执行条件
        if not self._validate_execution_conditions(state):
            return Command(goto="research_team")
        
        # 执行当前步骤
        return await self.step_executor.execute_current_step(state, config)
```

### 3.5 步骤执行器 (Step Executor)

#### 🎯 核心执行逻辑
```python
class StepExecutor:
    """步骤执行器 - 负责执行计划中的具体步骤"""
    
    async def execute_current_step(self, state: ReasoningState, config: RunnableConfig) -> Command:
        """执行当前步骤"""
        
        # 1. 找到当前未执行的步骤
        current_step, step_num = self._find_current_step(state)
        if not current_step:
            return Command(goto="research_team")
        
        # 2. 准备工具和创建执行器
        tools = await self._prepare_tools(config)
        executor = await create_agent_with_tools(
            self.agent.agent_type, 
            tools, 
            config,
            step_title=current_step.title,
            step_description=current_step.description
        )
        
        # 3. 执行步骤
        step_input = self._prepare_step_input(current_step, state)
        result = await executor.ainvoke(step_input, config=config)
        
        # 4. 处理执行结果
        return self._process_execution_result(result, current_step, step_num, state)
```

## 4. 协作机制

### 4.1 状态传递

#### 🔄 Command 机制
```python
# 智能体返回命令指定下一步行动
return Command(
    update={"current_plan": new_plan},  # 更新状态
    goto="next_agent"                   # 跳转到下一个智能体
)
```

#### 📊 状态共享
- **统一状态**：所有智能体共享 `ReasoningState`
- **增量更新**：通过 `Command.update` 增量更新状态
- **版本控制**：支持状态版本管理和回滚

### 4.2 工具集成

#### 🛠️ MCP工具管理
```python
async def setup_mcp_tools(config: RunnableConfig) -> List[Any]:
    """设置MCP工具"""
    configurable = Configuration.from_runnable_config(config)
    mcp_manager = MCPToolManager(configurable.mcp_servers)
    
    # 连接并获取工具
    await mcp_manager.connect_all()
    tools = await mcp_manager.get_all_tools()
    
    return tools
```

#### 🔧 React智能体创建
```python
async def create_agent_with_tools(agent_type: str, tools: List, config: RunnableConfig, **kwargs):
    """创建带工具的React智能体"""
    
    # 获取LLM
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])
    
    # 应用提示模板
    messages = apply_prompt_template(f"agent_{agent_type}", kwargs)
    
    # 创建React智能体
    return create_react_agent(
        llm, 
        tools,
        state_modifier=messages,
        checkpointer=None
    )
```

## 5. 并发处理

### 5.1 并行HTML生成

#### 🚀 并发报告生成
```python
async def parallel_html_generator_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """并行生成HTML报告片段"""
    
    # 创建并发任务
    tasks = {
        "problem_description": create_problem_description_task(state, config),
        "diagnosis_info": create_diagnosis_info_task(state, config),
        "key_findings": create_key_findings_task(state, config),
        "evidence_chain": create_evidence_chain_task(state, config),
        "summary_conclusion": create_summary_conclusion_task(state, config)
    }
    
    # 并发执行
    results = await asyncio.gather(*tasks.values())
    
    # 更新状态
    return Command(
        update={
            "problem_description_html": results[0],
            "diagnosis_info_html": results[1],
            "key_findings_html": results[2],
            "evidence_chain_html": results[3],
            "summary_conclusion_html": results[4]
        },
        goto="html_report_merger"
    )
```

### 5.2 智能合并

#### 🔗 HTML报告合并
```python
async def html_report_merger_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """智能合并HTML片段"""
    
    # 收集所有HTML片段
    html_sections = {
        "problem_description": state.get("problem_description_html", ""),
        "diagnosis_info": state.get("diagnosis_info_html", ""),
        "key_findings": state.get("key_findings_html", ""),
        "evidence_chain": state.get("evidence_chain_html", ""),
        "summary_conclusion": state.get("summary_conclusion_html", "")
    }
    
    # 智能合并
    merged_html = await merge_html_sections(html_sections, state, config)
    
    return Command(
        update={"merged_html_report": merged_html},
        goto="__end__"
    )
```

## 6. 错误处理与恢复

### 6.1 错误处理机制

#### 🛡️ 统一错误处理
```python
def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
    """统一错误处理"""
    self.logger.error(f"Agent {self.name} failed: {error}")
    
    # 记录错误到状态
    errors = state.get("errors", [])
    errors.append({
        "agent": self.name,
        "error": str(error),
        "timestamp": datetime.now().isoformat()
    })
    
    return Command(
        update={"errors": errors},
        goto="__end__"  # 或其他恢复路径
    )
```

### 6.2 状态持久化

#### 💾 检查点机制
```python
def build_graph_with_memory():
    """构建带内存的工作流图"""
    memory = MemorySaver()  # LangGraph内置内存存储
    builder = _build_base_graph()
    return builder.compile(checkpointer=memory)
```

## 7. 扩展机制

### 7.1 智能体扩展

#### 🔌 新智能体开发
```python
class CustomAgent(BaseAgent):
    """自定义智能体示例"""
    
    def __init__(self):
        super().__init__("custom_agent", "custom")
    
    async def _do_execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        # 实现自定义逻辑
        return Command(goto="next_agent")

# 注册到工作流
def add_custom_agent(builder: StateGraph):
    builder.add_node("custom_agent", custom_agent_node)
    builder.add_edge("some_agent", "custom_agent")
```

### 7.2 工具扩展

#### 🛠️ MCP工具集成
- **动态发现**：通过MCP协议自动发现工具
- **热插拔**：支持运行时添加/移除工具
- **统一接口**：所有工具通过统一接口访问

## 8. 最佳实践

### 8.1 设计原则
1. **职责单一**：每个智能体专注特定任务
2. **状态共享**：通过ReasoningState共享上下文
3. **错误容错**：完善的错误处理和恢复机制
4. **可观测性**：详细的日志和监控

### 8.2 性能优化
1. **并发执行**：利用asyncio并发处理独立任务
2. **智能缓存**：缓存工具执行结果和中间状态
3. **资源复用**：复用LLM连接和工具实例
4. **流式处理**：实时输出处理进度