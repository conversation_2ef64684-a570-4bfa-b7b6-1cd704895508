[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ecs-deep-diagnose"
version = "0.1.0"
description = "ecs-deep-diagnose project"
readme = "./README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "httpx>=0.28.1",
    "langchain-community>=0.3.19",
    "langchain-experimental>=0.3.4",
    "langchain-openai>=0.3.8",
    "langchain-postgres>=0.0.15",
    "psycopg[binary]>=3.0.0",
    "psycopg2-binary>=2.9.0",
    "greenlet>=3.0.0",
    "langgraph>=0.3.5",
    "readabilipy>=0.3.0",
    "python-dotenv>=1.0.1",
    "socksio>=1.0.0",
    "markdownify>=1.1.0",
    "fastapi>=0.110.0",
    "uvicorn>=0.27.1",
    "sse-starlette>=1.6.5",
    "pandas>=2.2.3",
    "numpy>=2.2.3",
    "plotly>=5.17.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "yfinance>=0.2.54",
    "litellm>=1.63.11",
    "json-repair>=0.7.0",
    "jinja2>=3.1.3",
    "duckduckgo-search>=8.0.0",
    "inquirerpy>=0.3.4",
    "arxiv>=2.2.0",
    "mcp>=1.6.0",
    "langchain-mcp-adapters>=0.0.11",
    "langfuse==2.36.2",
    "dashscope==1.20.0",
    "python-jose==3.3.0",
    "Flask==2.3.3",
    "cryptography==44.0.3",
    "pycryptodome==3.20.0",
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.25.0",
    "redis==5.0.7",
    "reportlab>=4.0.0",
    "markdown>=3.5.0",
    "celery==5.3.6",
    "oss2==2.18.6",
    "flower==2.0.1",
    "tortoise-orm>=0.20.0",
    "aiomysql>=0.2.0",
    "aerich>=0.7.2",
    "gunicorn==23.0.0",
    "html5lib>=1.1",
    "beautifulsoup4>=4.12.0",
    "alibabacloud-dingtalk>=2.2.27",
]

[project.optional-dependencies]
dev = [
    "black>=24.2.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.0.0",
    "langgraph-cli[inmem]>=0.2.10",
]
test = [
    "pytest>=7.4.0",
    "pytest-cov>=4.1.0",
]

[tool.pytest.ini_options]
testpaths = ["tests/unit"]
python_files = ["test_*.py"]
addopts = "-v --cov=. --cov-report=term-missing --cov-report xml"
asyncio_mode = "auto"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.coverage.report]


[tool.coverage.run]
omit = [
    "tests/*"
    ]
disable_warnings = ["no-data-collected"]


[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 120
target-version = ["py312"]
include = '\.pyi?$'
extend-exclude = '''
# A regex preceded with ^/ will apply only to files and directories
# in the root of the project.
^/build/
^/migrations/
'''

[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["migrations/*"]

[tool.flake8]
max-line-length = 120
extend-ignore = ["E203", "E501", "W503"]
exclude = [".git", "__pycache__", "build", "dist", "migrations", ".venv", "venv"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
# disallow_untyped_defs = true
# disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "migrations/",
    "tests/",
]

[[tool.mypy.overrides]]
module = [
    "tests.*",
]
ignore_errors = true

[tool.aerich]
tortoise_orm = "deep_diagnose.data.database.TORTOISE_ORM"
location = "./migrations"
src_folder = "./src"
